from .token import AccToken
import requests


class AccClientFactory:
    def __init__(self, user_nonce, user_key):
        self._token = AccToken(user_nonce, user_key)

    def create(self, address, username, password, client_name="Python client"):
        response = _query("POST", _url(address, "login"), json={
            "authorizationToken": self._token.generate(),
            "username": username,
            "password": password,
            "clientName": client_name
        })
        return AccClient(address, response['session'])


class AccClient:
    def __init__(self, address, session):
        self.address = address
        self.session = session

    def get_cameras(self, verbosity="HIGH"):
        response = _query("GET", _url(self.address, "cameras"), {
            "verbosity": verbosity,
            "session": self.session
        })
        return response["cameras"]
    
    # get /camera/link
    def get_camera_link(self, camera_id):
        response = _query("GET", _url(self.address, f"camera/{camera_id}/link"), {
            "session": self.session
        })
        return response["link"]
    
    # get /media reqired cameraId
    def get_media(self, camera_id):
        # https://*************:8443/mt/api/rest/v1/media?session=AXkAhv8KEMYc_4NGnEReooaJrr444Y8SFgoUXXDyXD0HqKnIFnCZ_bi4FfTYE0kaEPigWrJQpkmsptwDXklWXJkiEAv1WqzJ307kgMLVYt_KL_8qBmRhc2RhczIhGhBNnqyEcsBG_7-IjfJFV8tXKg1hZG1pbmlzdHJhdG9y&cameraId=49Ix0TNPMTQ0AAJds6RkS11DwyRjXQvDREtdCwPzZDMjS0NTEwsLveTEXAMDIYGX_NOa9QK9LJZaH_gzzXLePQA&format=fmp4
        # create request params
        request_params = f"{self.address}/mt/api/rest/v1/media?session={self.session}&cameraId={camera_id}&format=fmp4"
        return request_params
    
    # get //siteView
    def get_site_view(self):
        response = _query("GET", _url(self.address, "siteView"))
        print(f'response: {response}')
        return None
    
    # put /camera/commands/focus/continuous
    # "id": "string",
    # "session": "string",
    # "action": "START",
    # "distance": "NEAR"
    
    def put_camera_commands_focus_continuous(self, camera_id, action, distance):
        response = _query("PUT", _url(self.address, f"camera/{camera_id}/commands/focus/continuous"), {
            "id": camera_id,
            "session": self.session,
            "action": action,
            "distance": distance
        })
        return response
    
    # /camera/commands/auto-focus
    def auto_focus(self, camera_id):
        print(f'START:')
        response = _query("PUT", _url(self.address, f"camera/{camera_id}/commands/auto-focus"), {
            "id": camera_id,
            "session": self.session
        })
        print(f'END:')
        return response
    

    # {
    # "session": "string",
    # "id": "string",
    # "fieldOfView": {
    #     "imagePanelSize": {
    #     "width": 0,
    #     "height": 0
    #     },
    #     "ROI": {
    #     "x": 0,
    #     "y": 0,
    #     "width": 0,
    #     "height": 0
    #     },
    #     "point": {
    #     "x": 0,
    #     "y": 0
    #     }
    # },
    # "continuous": {
    #     "panAmount": 1,
    #     "tiltAmount": 1,
    #     "zoomAmount": 1,
    #     "action": "START"
    # }
    # }
    # https://docs.avigilon.com/bundle/web-endpoint-api/page/route/camera-commands-pan-tilt-zoom.htm
    def ptz_continuous(self, camera_id, panAmount, tiltAmount, zoomAmount, action):
        response = _query("PUT", _url(self.address, f"camera/commands/pan-tilt-zoom"), json={
            "id": camera_id,
            "session": self.session,
            "continuous": {
                "panAmount": panAmount,
                "tiltAmount": tiltAmount,
                "zoomAmount": zoomAmount,
                "action": action
            }
        })
        return response
                              



def _url(address, path):
    return f"{address}/mt/api/rest/v1/{path}"


def _query(method, url, params=None, json=None):
    response = requests.request(
        method, url, params=params, json=json, verify=False)
    response.raise_for_status()
    response_data = response.json()
    if (response_data["status"] != "success"):
        raise Exception(response_data["status"])
    return response_data["result"]
