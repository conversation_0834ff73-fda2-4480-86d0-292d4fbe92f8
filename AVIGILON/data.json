[{"id": "49Ix0UtLS0tJM0g20E1LNDLUTUtLStO1TEpL1DUzSUuzsDA2SEs2tNBLTsw1MBASeMk_rVkv0MtiqfWBP9Ms590DAA", "name": "Dahua", "location": "country/china", "apiType": "ONVIF_SOAP", "available": true, "connectionFailoverLevel": 0, "connectionState": "CONNECTED", "dateTimeZone": {"utcOffset": 25200, "timezone": "Asia/Jakarta", "stdMonth": 0, "stdWeek": 0, "stdDay": 0, "stdHour": 0, "stdMinute": 0, "stdBias": 0, "dstMonth": 0, "dstWeek": 0, "dstDay": 0, "dstHour": 0, "dstMinute": 0, "dstBias": 3600}, "firmwareUpgradeStatus": {"state": "IDLE", "timestamp": "1970-01-01T00:00:00.000Z", "progress": 0, "error": "UNKNOWN (1)"}, "firmwareVersion": "2.812.100Z004.0.R, Build Date 2023-03-13", "ipAddress": "*************:80", "manufacturer": "Dahua (ONVIF)", "model": "DH-SD49225XA-HNR-S3", "operatingPriority": 0, "physicalAddress": "F4:B1:C2:0D:FF:18", "serial": "9C06A05PAJ78E11", "serverId": "6Q-Wgy5RSjilO8D8ljme3g", "timezone": "Asia/Jakarta", "active": true, "logicalId": 4294967295, "geolocation": {}, "perspective": "CEILING", "enableClientDewarping": false, "links": [{"type": "DIGITAL_INPUT", "id": "WJWIeLYxfzeZHMyPyW-sNw", "source": "49Ix0UtLS0tJM0g20E1LNDLUTUtLStO1TEpL1DUzSUuzsDA2SEs2tNBLycwzMBASeMk_rVkv0MtiqfWBP9Ms590DAA", "target": "49Ix0UtLS0tJM0g20E1LNDLUTUtLStO1TEpL1DUzSUuzsDA2SEs2tNBLTsw1MBASeMk_rVkv0MtiqfWBP9Ms590DAA"}, {"type": "DIGITAL_INPUT", "id": "h_mbQvh5UMwsQXxr3EXn3w", "source": "49Ix0UtLS0tJM0g20E1LNDLUTUtLStO1TEpL1DUzSUuzsDA2SEs2tNBLycwzMBQSeMk_rVkv0MtiqfWBP9Ms590DAA", "target": "49Ix0UtLS0tJM0g20E1LNDLUTUtLStO1TEpL1DUzSUuzsDA2SEs2tNBLTsw1MBASeMk_rVkv0MtiqfWBP9Ms590DAA"}, {"type": "DIGITAL_OUTPUT", "id": "Mgs2guFTgI8XwNDhmLDkcw", "source": "49Ix0UtLS0tJM0g20E1LNDLUTUtLStO1TEpL1DUzSUuzsDA2SEs2tNBLyS8xMBASeMk_rVkv0MtiqfWBP9Ms590DAA", "target": "49Ix0UtLS0tJM0g20E1LNDLUTUtLStO1TEpL1DUzSUuzsDA2SEs2tNBLTsw1MBASeMk_rVkv0MtiqfWBP9Ms590DAA"}], "capabilities": {"general": ["REBOOT", "CHANGE_PASSWORD", "DEWARP"], "network": ["AUTO_IP_CONF", "MANUAL_IP_CONF", "HTTP_PORT", "MULTICAST"], "compression": ["H264", "H265", "STORE_PRIMARY_ONLY"], "h264": ["RESOLUTION", "MULTI_STREAMING", "VARIABLE_BIT_RATE", "VBR_IMAGE_RATE", "VBR_IMAGE_QUALITY", "VBR_MAX_BIT_RATE", "VBR_KEY_FRAME_INTERVAL_FRAMES", "RECORDING_PROFILES", "HDSM_TILING", "HDSM_10"], "acquisition": ["COLOUR_SATURATION", "SHARPNESS", "FOCUS_MANUAL", "FOCUS_MANUAL_SPEED"], "exposure": ["IRIS_AUTO", "IRIS_MANUAL", "IRIS_RANGED", "IRIS_AUTO_TIED_TO_EXPOSURE"], "digitalIo": ["INPUT_DEFAULT_CIRCUIT_STATE", "OUTPUT_DEFAULT_CIRCUIT_STATE", "OUTPUT_ACTIVATE", "OUTPUT_MODE"], "streamRecording": ["RESOLUTION", "VARIABLE_BIT_RATE", "VBR_IMAGE_RATE", "VBR_IMAGE_QUALITY", "VBR_MAX_BIT_RATE", "VBR_KEY_FRAME_INTERVAL_FRAMES"], "profileRecording": ["RESOLUTION", "VARIABLE_BIT_RATE", "VBR_IMAGE_RATE", "VBR_IMAGE_QUALITY", "VBR_MAX_BIT_RATE", "VBR_KEY_FRAME_INTERVAL_FRAMES"]}, "connected": true, "connectionStatus": {"isConnectable": true, "state": "CONNECTED", "errorFlags": [], "startTime": "2024-07-10T06:39:08.950Z"}, "ptzInfo": {"capabilities": ["AUXILIARY", "FOCUS_CONTINUOUS", "PAN_TILT", "PAN_TILT_ABSOLUTE", "PAN_TILT_CONTINUOUS", "PAN_TILT_RELATIVE", "PAN_TILT_RELATIVE_FOV", "PRESETS", "TOURS", "ZOOM_ABSOLUTE", "ZOOM_CONTINUOUS", "ZOOM_RELATIVE"], "auxChannels": {"min": 0, "max": 1}}, "recordedData": true, "defaultWidth": 2048, "defaultHeight": 1536}, {"id": "4xIx1DNOTkk0S0lJNElOSdVLTsw1MBASeMk_rVkv0MtiqfWBP9Ms590DAA", "name": "ipcamera", "location": "country/", "apiType": "ONVIF_SOAP", "available": true, "connectionFailoverLevel": 0, "connectionState": "CONNECTED", "dateTimeZone": {"utcOffset": 25200, "timezone": "Asia/Jakarta", "stdMonth": 0, "stdWeek": 0, "stdDay": 0, "stdHour": 0, "stdMinute": 0, "stdBias": 0, "dstMonth": 0, "dstWeek": 0, "dstDay": 0, "dstHour": 0, "dstMinute": 0, "dstBias": 3600}, "firmwareUpgradeStatus": {"state": "IDLE", "timestamp": "1970-01-01T00:00:00.000Z", "progress": 0, "error": "UNKNOWN (1)"}, "firmwareVersion": "V21.12 build 220909.34129", "ipAddress": "*************:80", "manufacturer": "IPC Professional (ONVIF)", "model": "IPCamera", "operatingPriority": 0, "physicalAddress": "3C:DA:6D:DA:4C:DE", "serial": "ID0602181092180762220903", "serverId": "6Q-Wgy5RSjilO8D8ljme3g", "timezone": "Asia/Jakarta", "active": true, "logicalId": 4294967295, "geolocation": {}, "perspective": "CEILING", "enableClientDewarping": false, "links": [{"type": "AUDIO_OUTPUT", "id": "P5XJQwdRipAhSfyKYQ1Cow", "source": "4xIx1DNOTkk0S0lJNElOSdUrLsg2MBASeMk_rVkv0MtiqfWBP9Ms590DAA", "target": "4xIx1DNOTkk0S0lJNElOSdVLTsw1MBASeMk_rVkv0MtiqfWBP9Ms590DAA"}, {"type": "DIGITAL_OUTPUT", "id": "vCEkvmJI52z0JUqhGCXPQA", "source": "4xIx1DNOTkk0S0lJNElOSdVLyS8xMBASeMk_rVkv0MtiqfWBP9Ms590DAA", "target": "4xIx1DNOTkk0S0lJNElOSdVLTsw1MBASeMk_rVkv0MtiqfWBP9Ms590DAA"}, {"type": "AUDIO_INPUT", "id": "bSabhoMEvqVpCMBuHuCzyA", "source": "4xIx1DNOTkk0S0lJNElOSdXLzUw2MBASeMk_rVkv0MtiqfWBP9Ms590DAA", "target": "4xIx1DNOTkk0S0lJNElOSdVLTsw1MBASeMk_rVkv0MtiqfWBP9Ms590DAA"}, {"type": "DIGITAL_INPUT", "id": "ITIgt7Tl1Vtk5WI-j0uWzQ", "source": "4xIx1DNOTkk0S0lJNElOSdVLycwzMBASeMk_rVkv0MtiqfWBP9Ms590DAA", "target": "4xIx1DNOTkk0S0lJNElOSdVLTsw1MBASeMk_rVkv0MtiqfWBP9Ms590DAA"}], "capabilities": {"general": ["REBOOT", "CHANGE_PASSWORD", "DEWARP"], "network": ["AUTO_IP_CONF", "MANUAL_IP_CONF", "HTTP_PORT", "MULTICAST"], "compression": ["MJPEG", "H264", "H265", "MJPEG_DEC", "STORE_PRIMARY_ONLY"], "mjpeg": ["RESOLUTION", "VARIABLE_BIT_RATE", "VBR_IMAGE_RATE", "VBR_IMAGE_QUALITY"], "h264": ["RESOLUTION", "MULTI_STREAMING", "VARIABLE_BIT_RATE", "VBR_IMAGE_RATE", "VBR_IMAGE_QUALITY", "VBR_MAX_BIT_RATE", "VBR_KEY_FRAME_INTERVAL_FRAMES", "RECORDING_PROFILES", "HDSM_TILING", "HDSM_10"], "acquisition": ["COLOUR_SATURATION", "SHARPNESS", "FOCUS_MANUAL_SPEED"], "digitalIo": ["INPUT_DEFAULT_CIRCUIT_STATE", "OUTPUT_DEFAULT_CIRCUIT_STATE", "OUTPUT_ACTIVATE", "OUTPUT_MODE"], "speaker": ["SPEAKER_OUTPUT"], "streamRecording": ["RESOLUTION", "VARIABLE_BIT_RATE", "VBR_IMAGE_RATE", "VBR_IMAGE_QUALITY", "VBR_MAX_BIT_RATE", "VBR_KEY_FRAME_INTERVAL_FRAMES"], "profileRecording": ["RESOLUTION", "VARIABLE_BIT_RATE", "VBR_IMAGE_RATE", "VBR_IMAGE_QUALITY", "VBR_MAX_BIT_RATE", "VBR_KEY_FRAME_INTERVAL_FRAMES"]}, "connected": true, "connectionStatus": {"isConnectable": true, "state": "CONNECTED", "errorFlags": [], "startTime": "2024-07-10T06:44:13.244Z"}, "ptzInfo": {"capabilities": ["AUXILIARY", "FOCUS_CONTINUOUS", "PAN_TILT", "PAN_TILT_ABSOLUTE", "PAN_TILT_CONTINUOUS", "PAN_TILT_RELATIVE", "PRESETS", "TOURS", "ZOOM_ABSOLUTE", "ZOOM_CONTINUOUS", "ZOOM_RELATIVE"], "presets": [{"name": "PRESET01      ", "id": 0}, {"name": "PRESET02      ", "id": 1}, {"name": "PRESET03      ", "id": 2}, {"name": "PRESET04      ", "id": 3}, {"name": "PRESET05      ", "id": 4}, {"name": "PRESET06      ", "id": 5}, {"name": "PRESET07      ", "id": 6}, {"name": "PRESET08      ", "id": 7}, {"name": "PRESET09      ", "id": 8}, {"name": "PRESET10      ", "id": 9}, {"name": "PRESET11      ", "id": 10}, {"name": "PRESET12      ", "id": 11}, {"name": "PRESET13      ", "id": 12}, {"name": "PRESET14      ", "id": 13}, {"name": "PRESET15      ", "id": 14}, {"name": "PRESET16      ", "id": 15}, {"name": "PRESET17      ", "id": 16}, {"name": "PRESET18      ", "id": 17}, {"name": "PRESET19      ", "id": 18}, {"name": "PRESET20      ", "id": 19}, {"name": "PRESET21      ", "id": 20}, {"name": "PRESET22      ", "id": 21}, {"name": "PRESET23      ", "id": 22}, {"name": "PRESET24      ", "id": 23}, {"name": "PRESET25      ", "id": 24}, {"name": "PRESET26      ", "id": 25}, {"name": "PRESET27      ", "id": 26}, {"name": "PRESET28      ", "id": 27}, {"name": "PRESET29      ", "id": 28}, {"name": "PRESET30      ", "id": 29}, {"name": "PRESET31      ", "id": 30}, {"name": "PRESET32      ", "id": 31}, {"name": "PRESET33      ", "id": 32}], "tours": [{"name": "", "id": 0, "presetIds": []}, {"name": "", "id": 1, "presetIds": []}, {"name": "", "id": 2, "presetIds": []}, {"name": "", "id": 3, "presetIds": []}, {"name": "", "id": 4, "presetIds": []}, {"name": "", "id": 5, "presetIds": []}, {"name": "", "id": 6, "presetIds": []}, {"name": "", "id": 7, "presetIds": []}], "auxChannels": {"min": 0, "max": 5}}, "recordedData": false, "defaultWidth": 2048, "defaultHeight": 1536}, {"id": "49Ix0TNPMTQ0AAJds6RkS11DwyRjXQvDREtdCwPzZDMjS0NTEwsLveTEXAMDIYGX_NOa9QK9LJZaH_gzzXLePQA", "name": "HIKVISION DS-2DE3A404IW-DE/W", "location": "city/hangzhou", "apiType": "ONVIF_SOAP", "available": true, "connectionFailoverLevel": 0, "connectionState": "CONNECTED", "dateTimeZone": {"utcOffset": 25200, "timezone": "Asia/Jakarta", "stdMonth": 0, "stdWeek": 0, "stdDay": 0, "stdHour": 0, "stdMinute": 0, "stdBias": 0, "dstMonth": 0, "dstWeek": 0, "dstDay": 0, "dstHour": 0, "dstMinute": 0, "dstBias": 3600}, "firmwareUpgradeStatus": {"state": "IDLE", "timestamp": "1970-01-01T00:00:00.000Z", "progress": 0, "error": "UNKNOWN (1)"}, "firmwareVersion": "V5.7.3 build 220215", "ipAddress": "*************:80", "manufacturer": "HIKVISION (ONVIF)", "model": "DS-2DE3A404IW-DE/W", "operatingPriority": 0, "physicalAddress": "80:7C:62:91:54:88", "serial": "DS-2DE3A404IW-DE/W20220425CCWRS6J84906253", "serverId": "6Q-Wgy5RSjilO8D8ljme3g", "timezone": "Asia/Jakarta", "active": true, "logicalId": 4294967295, "geolocation": {}, "perspective": "CEILING", "enableClientDewarping": false, "links": [{"type": "AUDIO_INPUT", "id": "CoflSIRSx4CJhapzeK0QSQ", "source": "49Ix0TNPMTQ0AAJds6RkS11DwyRjXQvDREtdCwPzZDMjS0NTEwsLvdzMZAMDIYGX_NOa9QK9LJZaH_gzzXLePQA", "target": "49Ix0TNPMTQ0AAJds6RkS11DwyRjXQvDREtdCwPzZDMjS0NTEwsLveTEXAMDIYGX_NOa9QK9LJZaH_gzzXLePQA"}, {"type": "AUDIO_OUTPUT", "id": "eYcjm3CNkahy16pwazvAag", "source": "49Ix0TNPMTQ0AAJds6RkS11DwyRjXQvDREtdCwPzZDMjS0NTEwsLveKCbAMDIYGX_NOa9QK9LJZaH_gzzXLePQA", "target": "49Ix0TNPMTQ0AAJds6RkS11DwyRjXQvDREtdCwPzZDMjS0NTEwsLveTEXAMDIYGX_NOa9QK9LJZaH_gzzXLePQA"}, {"type": "DIGITAL_INPUT", "id": "pwkCq_2ruVXF8qlWyJR7RQ", "source": "49Ix0TNPMTQ0AAJds6RkS11DwyRjXQvDREtdCwPzZDMjS0NTEwsLvZTMPAMDIYGX_NOa9QK9LJZaH_gzzXLePQA", "target": "49Ix0TNPMTQ0AAJds6RkS11DwyRjXQvDREtdCwPzZDMjS0NTEwsLveTEXAMDIYGX_NOa9QK9LJZaH_gzzXLePQA"}, {"type": "DIGITAL_OUTPUT", "id": "DfEf7saetWmXYBQzluZqpg", "source": "49Ix0TNPMTQ0AAJds6RkS11DwyRjXQvDREtdCwPzZDMjS0NTEwsLvZT8EgMDIYGX_NOa9QK9LJZaH_gzzXLePQA", "target": "49Ix0TNPMTQ0AAJds6RkS11DwyRjXQvDREtdCwPzZDMjS0NTEwsLveTEXAMDIYGX_NOa9QK9LJZaH_gzzXLePQA"}], "capabilities": {"general": ["REBOOT", "CHANGE_PASSWORD", "DEWARP"], "network": ["AUTO_IP_CONF", "MANUAL_IP_CONF", "HTTP_PORT", "MULTICAST"], "compression": ["H264", "H265", "STORE_PRIMARY_ONLY"], "h264": ["RESOLUTION", "MULTI_STREAMING", "VARIABLE_BIT_RATE", "VBR_IMAGE_RATE", "VBR_IMAGE_QUALITY", "VBR_MAX_BIT_RATE", "VBR_KEY_FRAME_INTERVAL_FRAMES", "RECORDING_PROFILES", "HDSM_TILING", "HDSM_10"], "acquisition": ["BACKLIGHT_COMP", "DEFOG_MODE", "COLOUR_SATURATION", "SHARPNESS", "IR_CUT_FILTER", "WHITE_BALANCE_AUTO", "WHITE_BALANCE_MANUAL", "WIDE_DYNAMIC_RANGE", "WIDE_DYNAMIC_RANGE_LEVEL", "FOCUS_AUTO", "FOCUS_AUTO_NEAR_LIMIT", "FOCUS_AUTO_FAR_LIMIT", "FOCUS_MANUAL_SPEED"], "exposure": ["EXPOSURE_AUTO", "EXPOSURE_MANUAL"], "digitalIo": ["INPUT_DEFAULT_CIRCUIT_STATE", "OUTPUT_DEFAULT_CIRCUIT_STATE", "OUTPUT_ACTIVATE", "OUTPUT_MODE"], "speaker": ["SPEAKER_OUTPUT"], "streamRecording": ["RESOLUTION", "VARIABLE_BIT_RATE", "VBR_IMAGE_RATE", "VBR_IMAGE_QUALITY", "VBR_MAX_BIT_RATE", "VBR_KEY_FRAME_INTERVAL_FRAMES"], "profileRecording": ["RESOLUTION", "VARIABLE_BIT_RATE", "VBR_IMAGE_RATE", "VBR_IMAGE_QUALITY", "VBR_MAX_BIT_RATE", "VBR_KEY_FRAME_INTERVAL_FRAMES"]}, "connected": true, "connectionStatus": {"isConnectable": true, "state": "CONNECTED", "errorFlags": [], "startTime": "2024-07-09T11:54:22.351Z"}, "ptzInfo": {"capabilities": ["FOCUS_CONTINUOUS", "PAN_TILT", "PAN_TILT_ABSOLUTE", "PAN_TILT_CONTINUOUS", "PAN_TILT_RELATIVE", "PAN_TILT_RELATIVE_FOV", "PRESETS", "TOURS", "ZOOM_ABSOLUTE", "ZOOM_CONTINUOUS", "ZOOM_RELATIVE"], "presets": [{"name": "Preset 1", "id": 1}, {"name": "Preset 2", "id": 2}, {"name": "Preset 3", "id": 3}, {"name": "Preset 4", "id": 4}, {"name": "Preset 5", "id": 5}, {"name": "Preset 6", "id": 6}, {"name": "Preset 7", "id": 7}, {"name": "Preset 8", "id": 8}, {"name": "Preset 9", "id": 9}, {"name": "Preset 10", "id": 10}, {"name": "Preset 11", "id": 11}, {"name": "Preset 12", "id": 12}, {"name": "Preset 13", "id": 13}, {"name": "Preset 14", "id": 14}, {"name": "Preset 15", "id": 15}, {"name": "Preset 16", "id": 16}, {"name": "Preset 17", "id": 17}, {"name": "Preset 18", "id": 18}, {"name": "Preset 19", "id": 19}, {"name": "Preset 20", "id": 20}, {"name": "Preset 21", "id": 21}, {"name": "Preset 22", "id": 22}, {"name": "Preset 23", "id": 23}, {"name": "Preset 24", "id": 24}, {"name": "Preset 25", "id": 25}, {"name": "Preset 26", "id": 26}, {"name": "Preset 27", "id": 27}, {"name": "Preset 28", "id": 28}, {"name": "Preset 29", "id": 29}, {"name": "Preset 30", "id": 30}, {"name": "Preset 31", "id": 31}, {"name": "Preset 32", "id": 32}, {"name": "Preset 33", "id": 33}, {"name": "Back to origin", "id": 34}, {"name": "Call patrol 1", "id": 35}, {"name": "Call patrol 2", "id": 36}, {"name": "Call patrol 3", "id": 37}, {"name": "Call patrol 4", "id": 38}, {"name": "Day mode", "id": 39}, {"name": "Night mode", "id": 40}, {"name": "Preset 41", "id": 41}, {"name": "Preset 42", "id": 42}, {"name": "Preset 43", "id": 43}, {"name": "Preset 44", "id": 44}, {"name": "One-touch patrol", "id": 45}, {"name": "Day/Night Auto Mode", "id": 46}, {"name": "Alarm light on", "id": 47}, {"name": "Alarm light off", "id": 48}, {"name": "Preset 49", "id": 49}, {"name": "Preset 50", "id": 50}, {"name": "Preset 51", "id": 51}, {"name": "Preset 52", "id": 52}, {"name": "Preset 53", "id": 53}, {"name": "Preset 54", "id": 54}, {"name": "Preset 55", "id": 55}, {"name": "Preset 56", "id": 56}, {"name": "Preset 57", "id": 57}, {"name": "Preset 58", "id": 58}, {"name": "Preset 59", "id": 59}, {"name": "Preset 60", "id": 60}, {"name": "Preset 61", "id": 61}, {"name": "Preset 62", "id": 62}, {"name": "Preset 63", "id": 63}, {"name": "Preset 64", "id": 64}, {"name": "Preset 65", "id": 65}, {"name": "Preset 66", "id": 66}, {"name": "Preset 67", "id": 67}, {"name": "Preset 68", "id": 68}, {"name": "Preset 69", "id": 69}, {"name": "Preset 70", "id": 70}, {"name": "Preset 71", "id": 71}, {"name": "Preset 72", "id": 72}, {"name": "Preset 73", "id": 73}, {"name": "Preset 74", "id": 74}, {"name": "Preset 75", "id": 75}, {"name": "Preset 76", "id": 76}, {"name": "Preset 77", "id": 77}, {"name": "Preset 78", "id": 78}, {"name": "Preset 79", "id": 79}, {"name": "Preset 80", "id": 80}, {"name": "Preset 81", "id": 81}, {"name": "Preset 82", "id": 82}, {"name": "Preset 83", "id": 83}, {"name": "Preset 84", "id": 84}, {"name": "Preset 85", "id": 85}, {"name": "Preset 86", "id": 86}, {"name": "Preset 87", "id": 87}, {"name": "Preset 88", "id": 88}, {"name": "Preset 89", "id": 89}, {"name": "Preset 90", "id": 90}, {"name": "Preset 91", "id": 91}, {"name": "Set manual limits", "id": 92}, {"name": "Save manual limits", "id": 93}, {"name": "Remote reboot", "id": 94}, {"name": "Call OSD menu", "id": 95}, {"name": "Preset 96", "id": 96}, {"name": "Preset 97", "id": 97}, {"name": "Preset 98", "id": 98}, {"name": "Preset 99", "id": 99}, {"name": "Preset 100", "id": 100}, {"name": "Preset 101", "id": 101}, {"name": "Call patrol 5", "id": 102}, {"name": "Call patrol 6", "id": 103}, {"name": "Call patrol 7", "id": 104}, {"name": "Call patrol 8", "id": 105}, {"name": "Preset 106", "id": 106}, {"name": "Preset 107", "id": 107}, {"name": "Preset 108", "id": 108}, {"name": "Preset 109", "id": 109}, {"name": "Preset 110", "id": 110}, {"name": "Preset 111", "id": 111}, {"name": "Preset 112", "id": 112}, {"name": "Preset 113", "id": 113}, {"name": "Preset 114", "id": 114}, {"name": "Preset 115", "id": 115}, {"name": "Preset 116", "id": 116}, {"name": "Preset 117", "id": 117}, {"name": "Preset 118", "id": 118}, {"name": "Preset 119", "id": 119}, {"name": "Preset 120", "id": 120}, {"name": "Preset 121", "id": 121}, {"name": "Preset 122", "id": 122}, {"name": "Preset 123", "id": 123}, {"name": "Preset 124", "id": 124}, {"name": "Preset 125", "id": 125}, {"name": "Preset 126", "id": 126}, {"name": "Preset 127", "id": 127}, {"name": "Preset 128", "id": 128}, {"name": "Preset 129", "id": 129}, {"name": "Preset 130", "id": 130}, {"name": "Preset 131", "id": 131}, {"name": "Preset 132", "id": 132}, {"name": "Preset 133", "id": 133}, {"name": "Preset 134", "id": 134}, {"name": "Preset 135", "id": 135}, {"name": "Preset 136", "id": 136}, {"name": "Preset 137", "id": 137}, {"name": "Preset 138", "id": 138}, {"name": "Preset 139", "id": 139}, {"name": "Preset 140", "id": 140}, {"name": "Preset 141", "id": 141}, {"name": "Preset 142", "id": 142}, {"name": "Preset 143", "id": 143}, {"name": "Preset 144", "id": 144}, {"name": "Preset 145", "id": 145}, {"name": "Preset 146", "id": 146}, {"name": "Preset 147", "id": 147}, {"name": "Preset 148", "id": 148}, {"name": "Preset 149", "id": 149}, {"name": "Preset 150", "id": 150}, {"name": "Preset 151", "id": 151}, {"name": "Preset 152", "id": 152}, {"name": "Preset 153", "id": 153}, {"name": "Preset 154", "id": 154}, {"name": "Preset 155", "id": 155}, {"name": "Preset 156", "id": 156}, {"name": "Preset 157", "id": 157}, {"name": "Preset 158", "id": 158}, {"name": "Preset 159", "id": 159}, {"name": "Preset 160", "id": 160}, {"name": "Preset 161", "id": 161}, {"name": "Preset 162", "id": 162}, {"name": "Preset 163", "id": 163}, {"name": "Preset 164", "id": 164}, {"name": "Preset 165", "id": 165}, {"name": "Preset 166", "id": 166}, {"name": "Preset 167", "id": 167}, {"name": "Preset 168", "id": 168}, {"name": "Preset 169", "id": 169}, {"name": "Preset 170", "id": 170}, {"name": "Preset 171", "id": 171}, {"name": "Preset 172", "id": 172}, {"name": "Preset 173", "id": 173}, {"name": "Preset 174", "id": 174}, {"name": "Preset 175", "id": 175}, {"name": "Preset 176", "id": 176}, {"name": "Preset 177", "id": 177}, {"name": "Preset 178", "id": 178}, {"name": "Preset 179", "id": 179}, {"name": "Preset 180", "id": 180}, {"name": "Preset 181", "id": 181}, {"name": "Preset 182", "id": 182}, {"name": "Preset 183", "id": 183}, {"name": "Preset 184", "id": 184}, {"name": "Preset 185", "id": 185}, {"name": "Preset 186", "id": 186}, {"name": "Preset 187", "id": 187}, {"name": "Preset 188", "id": 188}, {"name": "Preset 189", "id": 189}, {"name": "Preset 190", "id": 190}, {"name": "Preset 191", "id": 191}, {"name": "Preset 192", "id": 192}, {"name": "Preset 193", "id": 193}, {"name": "Preset 194", "id": 194}, {"name": "Preset 195", "id": 195}, {"name": "Preset 196", "id": 196}, {"name": "Preset 197", "id": 197}, {"name": "Preset 198", "id": 198}, {"name": "Preset 199", "id": 199}, {"name": "Preset 200", "id": 200}, {"name": "Preset 201", "id": 201}, {"name": "Preset 202", "id": 202}, {"name": "Preset 203", "id": 203}, {"name": "Preset 204", "id": 204}, {"name": "Preset 205", "id": 205}, {"name": "Preset 206", "id": 206}, {"name": "Preset 207", "id": 207}, {"name": "Preset 208", "id": 208}, {"name": "Preset 209", "id": 209}, {"name": "Preset 210", "id": 210}, {"name": "Preset 211", "id": 211}, {"name": "Preset 212", "id": 212}, {"name": "Preset 213", "id": 213}, {"name": "Preset 214", "id": 214}, {"name": "Preset 215", "id": 215}, {"name": "Preset 216", "id": 216}, {"name": "Preset 217", "id": 217}, {"name": "Preset 218", "id": 218}, {"name": "Preset 219", "id": 219}, {"name": "Preset 220", "id": 220}, {"name": "Preset 221", "id": 221}, {"name": "Preset 222", "id": 222}, {"name": "Preset 223", "id": 223}, {"name": "Preset 224", "id": 224}, {"name": "Preset 225", "id": 225}, {"name": "Preset 226", "id": 226}, {"name": "Preset 227", "id": 227}, {"name": "Preset 228", "id": 228}, {"name": "Preset 229", "id": 229}, {"name": "Preset 230", "id": 230}, {"name": "Preset 231", "id": 231}, {"name": "Preset 232", "id": 232}, {"name": "Preset 233", "id": 233}, {"name": "Preset 234", "id": 234}, {"name": "Preset 235", "id": 235}, {"name": "Preset 236", "id": 236}, {"name": "Preset 237", "id": 237}, {"name": "Preset 238", "id": 238}, {"name": "Preset 239", "id": 239}, {"name": "Preset 240", "id": 240}, {"name": "Preset 241", "id": 241}, {"name": "Preset 242", "id": 242}, {"name": "Preset 243", "id": 243}, {"name": "Preset 244", "id": 244}, {"name": "Preset 245", "id": 245}, {"name": "Preset 246", "id": 246}, {"name": "Preset 247", "id": 247}, {"name": "Preset 248", "id": 248}, {"name": "Preset 249", "id": 249}, {"name": "Preset 250", "id": 0}]}, "recordedData": false, "defaultWidth": 2560, "defaultHeight": 1440}]