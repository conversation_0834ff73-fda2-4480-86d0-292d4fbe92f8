import urllib3
import asyncio
from accapi.client import AccClientFactory
from tests.context import USER_KEY, USER_NONCE

# Disable SSL warnings for development
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

print(f'USER_KEY: {USER_KEY} - USER_NONCE: {USER_NONCE}')
factory = AccClientFactory(user_nonce=USER_NONCE, user_key=USER_KEY)
url = "https://172.24.108.167:8443"

def create_client_periodically():
    """Coroutine to create a client every 5 seconds."""
    client = factory.create(url, "administrator", "GPS@2025")
    cameras = client.get_cameras()
    print(f'cameras: {cameras}')
    # camera0 = cameras[0]
    print(f'camera0: {camera0}')
    # print(f'client: {client}')
    # await asyncio.sleep(3)
    # camera_id = camera0['id']
    # {'id': '49Ix0TMyMTe0MDAw0LUwS7HUNTRMMtW1MDE117W0SDM0NEpNNDAws9RLTsw1MBASeMk_rVkv0MtiqfWBP9Ms590DAA', 'name': 'HIKVISION DS-2CD2623G2-IZS', 'location': 'city/hangzhou', 'apiType': 'ONVIF_SOAP', 'available': True, 'connectionFailoverLevel': 0, 'connectionState': 'CONNECTED', 'dateTimeZone': {'utcOffset': 25200, 'timezone': 'Asia/Jakarta', 'stdMonth': 0, 'stdWeek': 0, 'stdDay': 0, 'stdHour': 0, 'stdMinute': 0, 'stdBias': 0, 'dstMonth': 0, 'dstWeek': 0, 'dstDay': 0, 'dstHour': 0, 'dstMinute': 0, 'dstBias': 3600}, 'firmwareUpgradeStatus': {'state': 'IDLE', 'timestamp': '1970-01-01T00:00:00.000Z', 'progress': 0, 'error': 'UNKNOWN (1)'}, 'firmwareVersion': 'V5.7.0 build 220714', 'ipAddress': '*************:80', 'manufacturer': 'HIKVISION (ONVIF)', 'model': 'DS-2CD2623G2-IZS', 'operatingPriority': 0, 'physicalAddress': '98:F1:12:EA:00:69', 'serial': 'DS-2CD2623G2-IZS20221111AAWRK85925424', 'serverId': '6Q-Wgy5RSjilO8D8ljme3g', 'timezone': 'Asia/Jakarta', 'active': True, 'logicalId': 4294967295, 'geolocation': {}, 'perspective': 'CEILING', 'enableClientDewarping': False, 'links': [{'type': 'AUDIO_INPUT', 'id': 'Ej3c6mIACNKCQoTCsxrgxg', 'source': '49Ix0TMyMTe0MDAw0LUwS7HUNTRMMtW1MDE117W0SDM0NEpNNDAws9TLzUw2MBASeMk_rVkv0MtiqfWBP9Ms590DAA', 'target': '49Ix0TMyMTe0MDAw0LUwS7HUNTRMMtW1MDE117W0SDM0NEpNNDAws9RLTsw1MBASeMk_rVkv0MtiqfWBP9Ms590DAA'}, {'type': 'DIGITAL_OUTPUT', 'id': 'TEfQVG6P8Dy5MOj2_Z89tQ', 'source': '49Ix0TMyMTe0MDAw0LUwS7HUNTRMMtW1MDE117W0SDM0NEpNNDAws9RLyS8xMBASeMk_rVkv0MtiqfWBP9Ms590DAA', 'target': '49Ix0TMyMTe0MDAw0LUwS7HUNTRMMtW1MDE117W0SDM0NEpNNDAws9RLTsw1MBASeMk_rVkv0MtiqfWBP9Ms590DAA'}, {'type': 'DIGITAL_INPUT', 'id': 'R2-yw90LrzZXH73AnVx_9g', 'source': '49Ix0TMyMTe0MDAw0LUwS7HUNTRMMtW1MDE117W0SDM0NEpNNDAws9RLycwzMBASeMk_rVkv0MtiqfWBP9Ms590DAA', 'target': '49Ix0TMyMTe0MDAw0LUwS7HUNTRMMtW1MDE117W0SDM0NEpNNDAws9RLTsw1MBASeMk_rVkv0MtiqfWBP9Ms590DAA'}, {'type': 'AUDIO_OUTPUT', 'id': 'mXZ7k8TUS6cL7mvn4lIZ2Q', 'source': '49Ix0TMyMTe0MDAw0LUwS7HUNTRMMtW1MDE117W0SDM0NEpNNDAws9QrLsg2MBASeMk_rVkv0MtiqfWBP9Ms590DAA', 'target': '49Ix0TMyMTe0MDAw0LUwS7HUNTRMMtW1MDE117W0SDM0NEpNNDAws9RLTsw1MBASeMk_rVkv0MtiqfWBP9Ms590DAA'}], 'capabilities': {'general': ['REBOOT', 'CHANGE_PASSWORD', 'DEWARP'], 'network': ['AUTO_IP_CONF', 'MANUAL_IP_CONF', 'HTTP_PORT', 'MULTICAST'], 'compression': ['H264', 'H265', 'STORE_PRIMARY_ONLY'], 'h264': ['RESOLUTION', 'MULTI_STREAMING', 'VARIABLE_BIT_RATE', 'VBR_IMAGE_RATE', 'VBR_IMAGE_QUALITY', 'VBR_MAX_BIT_RATE', 'VBR_KEY_FRAME_INTERVAL_FRAMES', 'RECORDING_PROFILES', 'HDSM_TILING', 'HDSM_10'], 'acquisition': ['BACKLIGHT_COMP', 'COLOUR_SATURATION', 'SHARPNESS', 'IR_CUT_FILTER', 'WHITE_BALANCE_AUTO', 'WHITE_BALANCE_MANUAL', 'WIDE_DYNAMIC_RANGE', 'WIDE_DYNAMIC_RANGE_LEVEL', 'FOCUS_MANUAL_SPEED'], 'exposure': ['EXPOSURE_AUTO'], 'digitalIo': ['INPUT_DEFAULT_CIRCUIT_STATE', 'OUTPUT_DEFAULT_CIRCUIT_STATE', 'OUTPUT_ACTIVATE', 'OUTPUT_MODE'], 'speaker': ['SPEAKER_OUTPUT'], 'streamRecording': ['RESOLUTION', 'VARIABLE_BIT_RATE', 'VBR_IMAGE_RATE', 'VBR_IMAGE_QUALITY', 'VBR_MAX_BIT_RATE', 'VBR_KEY_FRAME_INTERVAL_FRAMES'], 'profileRecording': ['RESOLUTION', 'VARIABLE_BIT_RATE', 'VBR_IMAGE_RATE', 'VBR_IMAGE_QUALITY', 'VBR_MAX_BIT_RATE', 'VBR_KEY_FRAME_INTERVAL_FRAMES']}, 'connected': True, 'connectionStatus': {'isConnectable': True, 'state': 'CONNECTED', 'errorFlags': [], 'startTime': '2024-11-08T09:50:22.919Z'}, 'ptzInfo': {'capabilities': ['FOCUS_CONTINUOUS', 'PAN_TILT_CONTINUOUS', 'PRESETS', 'TOURS', 'ZOOM_CONTINUOUS']}, 'recordedData': True, 'defaultWidth': 1920, 'defaultHeight': 1080}
    # dump ptzInfo
    # for camera in cameras:
    #     ptzInfo = camera['ptzInfo']
    #     print(f'ptzInfo: {ptzInfo}')
    # client.ptz_continuous(camera_id = camera_id, panAmount=0, tiltAmount=0, zoomAmount=0, action="START")


create_client_periodically()
