import ctypes
from ctypes import c_byte
from ctypes import c_uint
from ctypes import c_ushort
from ctypes import c_int

# Define constant for SERIALNO_LEN (replace with the correct value from SDK)
SERIALNO_LEN = 48  # Example length; adjust based on the SDK documentation

class NET_DVR_DEVICEINFO(ctypes.Structure):
    _fields_ = [
        ("sSerialNumber", c_byte * SERIALNO_LEN),  # Serial number as a byte array
        ("byAlarmInPortNum", c_byte),  # Number of alarm input ports (1 byte)
        ("byAlarmOutPortNum", c_byte),  # Number of alarm output ports (1 byte)
        ("byDiskNum", c_byte),  # Number of disks (1 byte)
        ("byDVRType", c_byte),  # DVR type (1 byte), e.g., 1: DVR, 2: ATM DVR, etc.
        ("byChanNum", c_byte),  # Number of channels (1 byte)
        ("byStartChan", c_byte),  # Start channel number (1 byte)
    ]


class NET_DVR_DEVICEINFO_V30(ctypes.Structure):
    _fields_ = [
        ("sSerialNumber", c_byte * SERIALNO_LEN),  # Serial number array
        ("byAlarmInPortNum", c_byte),               # Number of alarm input ports
        ("byAlarmOutPortNum", c_byte),              # Number of alarm output ports
        ("byDiskNum", c_byte),                       # Number of disks
        ("byDVRType", c_byte),                       # DVR type (1: DVR, 2: ATM DVR, etc.)
        ("byChanNum", c_byte),                       # Number of channels
        ("byStartChan", c_byte),                     # Start channel number (DVS-1, DVR-1)
        ("byAudioChanNum", c_byte),                  # Number of audio channels
        ("byIPChanNum", c_byte),                     # Number of IP channels
        ("byZeroChanNum", c_byte),                   # Reserved channel number
        ("byMainProto", c_byte),                     # Main protocol (0: private, 1: RTSP, etc.)
        ("bySubProto", c_byte),                      # Sub protocol
        ("bySupport", c_byte),                       # Support flags
        ("bySupport1", c_byte),                      # Additional support flags
        ("bySupport2", c_byte),                      # More support flags
        ("wDevType", c_ushort),                      # Device type
        ("bySupport3", c_byte),                      # Further support flags
        ("byMultiStreamProto", c_byte),              # Multi-stream protocol support
        ("byStartDChan", c_byte),                    # Start D channel number
        ("byStartDTalkChan", c_byte),                # Start D talk channel number
        ("byHighDChanNum", c_byte),                  # High-definition channel number
        ("bySupport4", c_byte),                      # Additional support flags
        ("byLanguageType", c_byte),                  # Language support
        ("byRes2", c_byte * 9),                     # Reserved bytes
    ]

class NET_DVR_DEVICEINFO_V40(ctypes.Structure):
    _fields_ = [
        ("struDeviceV30", NET_DVR_DEVICEINFO_V30),  # Include NET_DVR_DEVICEINFO_V30 structure
        ("bySupportLock", c_byte),                   # Support for locking (1 byte)
        ("byRetryLoginTime", c_byte),                # Retry login time (1 byte)
        ("byPasswordLevel", c_byte),                 # Password strength level (1 byte)
        ("byProxyType", c_byte),                     # Proxy type (1 byte)
        ("dwSurplusLockTime", c_uint),               # Surplus lock time (4 bytes)
        ("byCharEncodeType", c_byte),                # Character encoding type (1 byte)
        ("bySupportDev5", c_byte),                   # Support for v50 version devices (1 byte)
        ("bySupport", c_byte),                        # Extension support (1 byte)
        ("byLoginMode", c_byte),                     # Login mode (1 byte)
        ("dwOEMCode", c_int),                        # OEM code (4 bytes)
        ("iResidualValidity", c_int),                # Residual validity (4 bytes)
        ("byResidualValidity", c_byte),              # Residual validity status (1 byte)
        ("byRes2", c_byte * 243),                    # Reserved bytes (243 bytes)
    ]
