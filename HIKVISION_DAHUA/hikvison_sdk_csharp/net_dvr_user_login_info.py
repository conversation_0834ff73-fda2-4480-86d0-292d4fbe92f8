import ctypes
from ctypes import c_byte, c_ushort, c_int, c_bool, c_void_p

# Constants (replace with actual values from SDK documentation)
NET_DVR_DEV_ADDRESS_MAX_LEN = 129
NET_DVR_LOGIN_USERNAME_MAX_LEN = 64
NET_DVR_LOGIN_PASSWD_MAX_LEN = 64

# Define LOGINRESULTCALLBACK as a placeholder callback type (replace if needed)
LOGINRESULTCALLBACK = ctypes.CFUNCTYPE(None, c_int, c_int, c_void_p)

class NET_DVR_USER_LOGIN_INFO(ctypes.Structure):
    _fields_ = [
        ("sDeviceAddress", c_byte * NET_DVR_DEV_ADDRESS_MAX_LEN),  # Device IP address as a byte array
        ("byUseTransport", c_byte),  # Transport protocol (1 byte)
        ("wPort", c_ushort),  # Port number (2 bytes)
        ("sUserName", c_byte * NET_DVR_LOGIN_USERNAME_MAX_LEN),  # Username as a byte array
        ("sPassword", c_byte * NET_DVR_LOGIN_PASSWD_MAX_LEN),  # Password as a byte array
        ("cbLoginResult", LOGINRESULTCALLBACK),  # Callback function for login result
        ("pUser", c_void_p),  # User data pointer
        ("bUseAsynLogin", c_bool),  # Whether to use asynchronous login (1 byte boolean)
        ("byProxyType", c_byte),  # Proxy type (1 byte)
        ("byUseUTCTime", c_byte),  # UTC time usage flag (1 byte)
        ("byLoginMode", c_byte),  # Login mode (1 byte)
        ("byHttps", c_byte),  # HTTPS usage (1 byte)
        ("iProxyID", c_int),  # Proxy ID (4 bytes)
        ("byVerifyMode", c_byte),  # Verification mode (1 byte)
        ("byRes3", c_byte * 119),  # Reserved array (119 bytes)
    ]
