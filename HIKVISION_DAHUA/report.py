from axis_api.axis_api import VapixAPI
import dotenv
from hanwha.hanwha_api import HANWHAAPICamera
dotenv.load_dotenv()
import os
import openpyxl
import re
from collections import Counter
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
from pydantic import BaseModel, HttpUrl, ValidationError
from pydantic_core import Url

import requests
from requests.auth import HTTPDigestAuth

#Onvif
from onvif import ONVIFCamera, exceptions

#Dahua
from dahua_rpc import DahuaRpc

#Hikvision
import hikvision.api




def get_device_info_web(ip, info):
    try:
        username = info['username']
        password = info['password']
        port = info['port']
        path = info['path']
        scheme = info['scheme']
        # G<PERSON>i yêu cầu HTTP cơ bản để kiểm tra thiết bị có giao diện web không
        url = f"http://{ip}"
        response = requests.get(url, auth=(username, password), timeout=5)
        print(f'response: {response}')
        # <PERSON><PERSON><PERSON> tra nội dung phản hồi để đoán thiết bị là NVR hay IP camera
        if "nvr" in response.text.lower():
            device_type = "NVR"
        elif "camera" in response.text.lower():
            device_type = "IP Camera"
        else:
            device_type = "Unknown"

        return {"IP": ip, "Type": device_type, "Status": "Accessible"}
    
    except requests.exceptions.RequestException as e:
        return {"IP": ip, "Type": "Unknown", "Status": "Not Accessible", "Error": str(e)}



def get_onvif_info(ip, info):
    try:
        username = info['username']
        password = info['password']
        port = info['port']
        path = info['path']
        scheme = info['scheme']
        # Tạo kết nối với thiết bị qua ONVIF
        # # Tạo đối tượng ONVIFCamera với tên người dùng và mật khẩu
        camera = ONVIFCamera(ip, 80, username, password)
        # Lấy thông tin thiết bị
        device_info = camera.devicemgmt.GetDeviceInformation()
        # Lấy thông tin mô tả
        # manufacturer = device_info.Manufacturer
        # model = device_info.Model
        print(f'get_onvif_info: {device_info}')

        return {
            "Manufacturer": device_info.Manufacturer,
            "Model": device_info.Model,
            "SerialNumber": device_info.SerialNumber,
            "MACAddress": "",
            "NumberCameras": "1"
        }
    except Exception as e:
        return None


import requests
from requests.auth import HTTPDigestAuth

def get_dahua_info(ip, info):
    try:
        username = info['username']
        password = info['password']
        port = info['port']
        path = info['path']
        scheme = info['scheme']
        dahua = DahuaRpc(host=ip, username=username, password=password)
        dahua.login()
        product_definition_xml_data = dahua.request(method="magicBox.getProductDefinition")
        serial_number_xml_data = dahua.request(method="magicBox.getSerialNo")
        device_type_xml_data = dahua.request(method="magicBox.getDeviceType")
        machine_name_xml_data = dahua.request(method="magicBox.getMachineName")
        # Extract and print the relevant information
        model = device_type_xml_data.get("params", {}).get("type", "Unknown Type")
        device_name = machine_name_xml_data.get("params", {}).get("name", "Unknown Name")
        serial_number = serial_number_xml_data.get("params", {}).get("sn", "Unknown Serial")
        if model.contains('XVR') or model.contains('DVR') or model.contains('NVR'):
            number_cameras = product_definition_xml_data.get("params", {}).get("definition", {}).get("VideoInputChannels", "Unknown Channels")
        else:
            number_cameras = '1'
        return {
            "Manufacturer": "Dahua",
            "Model": model,
            "SerialNumber": serial_number,
            "MACAddress": "",
            "NumberCameras": number_cameras
        }
    except Exception as e:
        return None



def get_hikvision_info(ip, info):
    try:
        username = info['username']
        password = info['password']
        port = info['port']
        path = info['path']
        scheme = info['scheme']
        hik_camera = hikvision.api.CreateDevice(ip, username=username, password=password)
        xml_data = hik_camera.get_about()
        device_name, model, mac_address, serial_number = hik_camera.get_device_name(xml_data)
        is_nvr = device_name == "Network Video Recorder"
        number_cameras = '1'
        if is_nvr:
            number_cameras = hik_camera.get_channels_inputproxy()
            print(f'get_hikvision_info: Number Cameras: {number_cameras}')
        return {
            "Manufacturer": "Hikvision",
            "Model": model,
            "SerialNumber": serial_number,
            "MACAddress": mac_address,
            "NumberCameras": number_cameras
        }
    except Exception as e:
        return None

def get_axis_info(ip, info):
    try:
        username = info['username']
        password = info['password']
        port = info['port']
        path = info['path']
        scheme = info['scheme']
        vapix_api = VapixAPI(ip, username, password)
        # get device info
        info = vapix_api.get_device_info()
        vapix_api.session.close()
        print(f'get_axis_info: info: {info}')
        return {
            "Manufacturer": "Axis",
            "Model": "",
            "SerialNumber": "",
            "FirmwareVersion": "",
            "MACAddress": "",
            "NumberCameras": "1"
        }
    except Exception as e:
        return None
    
def get_hanwha_info(ip, info):
    try:
        username = info['username']
        password = info['password']
        port = info['port']
        path = info['path']
        scheme = info['scheme']
        hanwha_info = HANWHAAPICamera(ip, username, password)
        response = hanwha_info.attributes_information()
        if response.status_code == 200:
            data = response.json()
        elif response.status_code == 404:
            data = response.text
        else:
            data = None
        print(f'get_hanwha_info: {data} - {ip}')
        return {
            "Manufacturer": "Hanwha",
            "Model": "",
            "SerialNumber": "",
            "MACAddress": "",
            "NumberCameras": "1"
        }
    except Exception as e:
        return None



class RTSPParser:
    def __init__(self, url_list):
        self.url_list = url_list
        self.parsed_data = {}
        self.ip_count = Counter()

    def parse_urls(self):
        for url in self.url_list:
            data = Url(url)
            if data is not None:
                scheme = data.scheme
                username = data.username
                password = data.password
                ip = data.host
                port = data.port
                path = data.path
                # Lưu thông tin username và password duy nhất cho mỗi IP
                self.parsed_data[ip] = {
                    'username': username,
                    'password': password,
                    'port': port,
                    'path': path,
                    'scheme': scheme
                }
                print(f'user: {username} - pass: {password}')
                # Tăng số lần xuất hiện của IP
                self.ip_count[ip] += 1
                
    def get_device_info(self, ip, info):
        print(f'ip: {ip}, {info}')
        # Thử lấy thông tin từ Hikvision
        hikvision_info = get_hikvision_info(ip, info)
        print(f'START hikvision_info: {hikvision_info}')
        if hikvision_info is not None:
            if 'Manufacturer' in hikvision_info and hikvision_info['Manufacturer'] == 'Hikvision':
                return hikvision_info
        print(f'END hikvision_info: {hikvision_info}')

        # Thử lấy thông tin từ Dahua
        dahua_info = get_dahua_info(ip, info)
        print(f'START dahua_info: {dahua_info}')
        if dahua_info is not None:
            if 'Manufacturer' in dahua_info and dahua_info['Manufacturer'] == 'Dahua':
                return dahua_info
        print(f'END dahua_info: {dahua_info}')
        
        axis_info = get_axis_info(ip, info)
        print(f'START axis_info: {axis_info}')
        if axis_info is not None:
            if 'Manufacturer' in axis_info and axis_info['Manufacturer'] == 'Axis':
                return axis_info
        print(f'END axis_info: {axis_info}')

        # hanwha_info = get_hanwha_info(ip, info)
        # print(f'START hanwha_info: {hanwha_info}')
        # if hanwha_info is not None:
        #     if 'Manufacturer' in hanwha_info and hanwha_info['Manufacturer'] == 'Hanwha':
        #         return hanwha_info
        # print(f'END hanwha_info: {hanwha_info}')
        
        onvif_info = get_onvif_info(ip, info)
        print(f'onvif_info: {onvif_info}')
        if onvif_info is not None:
            return onvif_info
        
        info_from_web = get_device_info_web(ip, info)
        print(f'info_from_web: {info_from_web}')

        # Nếu không phải Hikvision hoặc Dahua, trả về không xác định
        return {"Error": "Unable to identify device type"}

    def save_to_excel(self, output_file):
        # Chuẩn bị dữ liệu để lưu vào DataFrame
        data = []
        
        # Use ThreadPoolExecutor to handle get_device_info concurrently
        with ThreadPoolExecutor(max_workers=10) as executor:
            # Submit tasks to the thread pool
            futures = {executor.submit(self.get_device_info, ip, info): ip for ip, info in self.parsed_data.items()}
            
            # Collect the results as they are completed
            for future in as_completed(futures):
                ip = futures[future]
                try:
                    device_info = future.result()
                    data.append({
                        'IP': ip,
                        'Username': self.parsed_data[ip]['username'],
                        'Password': self.parsed_data[ip]['password'],
                        'Manufacturer': device_info.get('Manufacturer', 'Unknown'),
                        'Model': device_info.get('Model', 'Unknown'),
                        'SerialNumber': device_info.get('SerialNumber', 'Unknown'),
                        'MACAddress': device_info.get('MACAddress', 'Unknown')
                    })
                except Exception as e:
                    print(f"Error retrieving device info for {ip}: {e}")
        
        # Tạo DataFrame từ dữ liệu
        df = pd.DataFrame(data)

        # Ghi DataFrame vào file Excel
        df.to_excel(output_file, index=False)


# Đọc RTSP link từ file
def read_rtsp_links_from_file(file_path):
    with open(file_path, 'r') as file:
        urls = file.read().splitlines()  # Đọc mỗi dòng và tạo danh sách
    return urls

# Đường dẫn tới file chứa các RTSP links
file_path = 'input_scan.txt'
output_file = 'output_scan.xlsx'

# Đọc các đường link từ file
url_list = read_rtsp_links_from_file(file_path)

# Tạo đối tượng RTSPParser và phân tích các URL
parser = RTSPParser(url_list)
parser.parse_urls()

# Lưu thông tin đã phân tích vào file Excel
parser.save_to_excel(output_file)
print(f"Thông tin đã được lưu vào {output_file}")
