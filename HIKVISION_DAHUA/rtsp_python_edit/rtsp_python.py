from queue import Queue
from typing import Callable, List
import threading
from rtsp_python.modules import utils,attack,worker
from argparse import Namespace
import logging
import collections
from rtsp_python.modules.rtsp import RTSPClient
import av
from pydantic import AnyUrl
from rtsp_python import DEFAULT_CREDENTIALS, DEFAULT_ROUTES, __version__
def start_threads(number: int, target: Callable, *args) -> List[threading.Thread]:
    threads = []
    for _ in range(number):
        thread = threading.Thread(target=target, args=args)
        thread.daemon = True
        threads.append(thread)
        thread.start()
    return threads

def wait_for(queue: Queue, threads: List[threading.Thread]):
    """Waits for queue and then threads to finish."""
    queue.join()
    [queue.put(None) for _ in range(len(threads))]
    [t.join() for t in threads]

class RtspPython:
    def __init__(self,debug=True, route_threads = 255,credential_threads = 255,onvif_threads = 255, screenshot_threads = 255, ports = [8554,554, 5554,''],timeout = 1, ip_list = [], route_list = utils.route_list,credential_list = [],callback_result: Callable = None, brand = None, mac_address=None, is_nvr_dvr = False):
        self.args = Namespace(debug=True,route_threads=route_threads,credential_threads = credential_threads, onvif_threads = onvif_threads, screenshot_threads = screenshot_threads,ports=ports,timeout=timeout,ip_list = ip_list,route_list=route_list, credential_list = credential_list, brand = brand, mac_address=mac_address, is_nvr_dvr = is_nvr_dvr)
        print(f'ip_list = {ip_list} - credential_list = {credential_list}')
        self.callback_result = callback_result
        self.rtsp_list = []
        self.args.credentials = DEFAULT_CREDENTIALS
        self.args.routes = DEFAULT_ROUTES
    def scan(self):
        logger = logging.getLogger(__name__)
        attack.logger_is_enabled = self.args.debug
        av.logging.set_level(av.logging.FATAL)
        # Check ip là rtsp
        is_rtsp = False
        rtsp = None
        try:
            for item in self.args.ip_list:
                url = AnyUrl(item)
                if url.scheme == 'rtsp':
                    ip = url.host
                    username = url.username
                    password = url.password
                    port = url.port
                    if ip is not None and username is not None and password is not None and port is not None:
                        is_rtsp = True
                        rtsp = item
                        ip_list = collections.deque(set(utils.load_ip_list([ip])))
                        if len(self.args.route_list) != 0:
                            attack.ROUTES = self.args.route_list
                        else:
                            attack.ROUTES = utils.load_txt(self.args.routes, "routes")
                        attack.CREDENTIALS = [f'{username}:{password}']
                        attack.PORTS = [port]
                        # route_queue = Queue()
                        # credential_queue = Queue()
                        onvif_queue = Queue()
                        screenshot_queue = Queue()
                        onvif_threads = start_threads(self.args.onvif_threads, worker.brute_onvif, onvif_queue, screenshot_queue,self.callback_result, self.args.brand, self.args.mac_address, self.args.is_nvr_dvr)
                        screenshot_threads = None
                        # screenshot_threads = start_threads(self.args.screenshot_threads,worker.screenshot_targets,screenshot_queue,self.callback_result)
                        rtsp_client = RTSPClient(ip = ip)
                        rtsp_client.port = port
                        rtsp_client.credentials = attack.CREDENTIALS[0]
                        path_parts = item.split(f':{port}')
                        route = path_parts[-1]
                        rtsp_client.routes = [route]
                        onvif_queue.put(rtsp_client)

                        wait_for(onvif_queue,onvif_threads)
                        print(f'wait_for onvif_queue done')
                        # wait_for(screenshot_queue,screenshot_threads)
                        break
        except Exception as e:
            logger.info(f"Error: {e}")

        if not is_rtsp:
            ip_list = collections.deque(set(utils.load_ip_list(self.args.ip_list)))
            # logger.info(f"Scanning = {ip_list}")
            if len(self.args.route_list) != 0:
                attack.ROUTES = self.args.route_list
            else:
                attack.ROUTES = utils.load_txt(self.args.routes, "routes")
            #attack.ROUTES = utils.load_txt(self.args.routes, "routes")
            if len(self.args.credential_list) != 0:
                attack.CREDENTIALS = self.args.credential_list
            else:
                attack.CREDENTIALS = self.args.credential_list
                # attack.CREDENTIALS = utils.load_txt(self.args.credentials, "credentials")

            attack.PORTS = self.args.ports

            route_queue = Queue()
            credential_queue = Queue()
            onvif_queue = Queue()
            screenshot_queue = Queue()

            route_threads = start_threads(self.args.route_threads,worker.brute_routes, route_queue, credential_queue,self.callback_result)
            credential_threads = start_threads(self.args.credential_threads, worker.brute_credentials, credential_queue, onvif_queue,self.callback_result)
            onvif_threads = start_threads(self.args.onvif_threads, worker.brute_onvif, onvif_queue, screenshot_queue,self.callback_result, self.args.brand, self.args.mac_address, self.args.is_nvr_dvr)
            # screenshot_threads = start_threads(self.args.screenshot_threads,worker.screenshot_targets,screenshot_queue,self.callback_result)

            while ip_list:
                route_queue.put(RTSPClient(ip = ip_list.popleft(),timeout= self.args.timeout))
            
            wait_for(route_queue,route_threads)
            # print(f'wait_for route_queue done')
            wait_for(credential_queue,credential_threads)
            # print(f'wait_for credential_queue done')
            wait_for(onvif_queue,onvif_threads)
            # print(f'wait_for onvif_queue done')
            # wait_for(screenshot_queue,screenshot_threads)

        # if is_rtsp:
        #     for item in self.rtsp_list:
        #         for rtsp_item in item['rtsp_list']:
        #             if rtsp_item == rtsp:
        #                 return [item]
                    
        #     if len(self.rtsp_list) > 0:
        #         return [self.rtsp_list[0]]

        # return self.rtsp_list
