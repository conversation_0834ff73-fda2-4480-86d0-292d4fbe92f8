from queue import Queue
from threading import RLock
from typing import List,Callable
#from rich.progress import TaskID
import logging
from rtsp_python.modules.attack import attack_credentials, attack_route, attack_onvif, get_screenshot
#from src.common.rtspbrute.modules.cli.output import ProgressBar
from rtsp_python.modules.rtsp import RTS<PERSON>lient
from rtsp_python.modules.utils import append_result
from onvif import ONVIFCamera, exceptions
import time
import json
# PROGRESS_BAR: ProgressBar
# CHECK_PROGRESS: TaskID
# BRUTE_PROGRESS: TaskID
# SCREENSHOT_PROGRESS: TaskID
LOCK = RLock()
logger = logging.getLogger(__name__)
def brute_routes(input_queue: Queue, output_queue: Queue,callback_result: Callable) -> None:
    while True:
        target: RTSPClient = input_queue.get()
        if target is None:
            break

        result = attack_route(target)
        if result:
            #PROGRESS_BAR.add_total(BRUTE_PROGRESS)
            output_queue.put(result)

        #PROGRESS_BAR.update(CHECK_PROGRESS, advance=1)
        input_queue.task_done()
    # print(f'brute_routes done')

def brute_credentials(input_queue: Queue, output_queue: Queue,callback_result: Callable) -> None:
    while True:
        target: RTSPClient = input_queue.get()
        # logger.info(f"brute_credentials = {target}")
        if target is None:
            break
        result = attack_credentials(target)
        
        if result:
            #PROGRESS_BAR.add_total(SCREENSHOT_PROGRESS)
            #rtsp_list.append(str(result))
            # logger.info(f"brute_credentials = {target}")
            output_queue.put(result)
        else:
            pass
            callback_result({'ip':target.ip,'port':target.port})

        #PROGRESS_BAR.update(BRUTE_PROGRESS, advance=1)
        input_queue.task_done()
    # print(f'brute_credentials done')

def brute_onvif(input_queue: Queue, output_queue: Queue,callback_result: Callable, brand = None, mac_address = None, is_nvr_dvr = None) -> None:
    while True:
        target_url: RTSPClient = input_queue.get()
        # logger.info(f"brute_onvif target_url = {target_url}")
        if target_url is None:
            break
        result = attack_onvif(target_url.ip)
        if not result:
            if callback_result is not None:
                username, password = target_url.credentials.split(":")
                data = {}
                data.update({'username': username, 'password': password, 'ip': target_url.ip, 'port': 80,'manufacturer': brand,'camera_type':'Not Onvif','camera_model':'', 'serial_number': '', 'mac_address' : mac_address, 'is_nvr_dvr':is_nvr_dvr, 'number_cameras':'1'})
                callback_result(data)
            output_queue.put(target_url)
        else:
            try:
                if ':' in target_url.credentials:
                    username, password = target_url.credentials.split(":")
                    # # Tạo đối tượng ONVIFCamera với tên người dùng và mật khẩu
                    camera = ONVIFCamera(target_url.ip, 80, username, password)
                    media_service = camera.create_media_service()
                    profiles = media_service.GetProfiles()
                    # Get the number of channels
                    number_cameras = len(profiles)
                    if number_cameras >= 2:
                        number_cameras = int(number_cameras / 2)
                    print(f'number_cameras: {number_cameras}')
                    # Lấy thông tin thiết bị
                    device_info = camera.devicemgmt.GetDeviceInformation()
                    print(f'device_info: {device_info}')
                    # Lấy thông tin mô tả
                    manufacturer = device_info.Manufacturer
                    model = device_info.Model
                    serial_number = device_info.SerialNumber
                    # firmware_version = device_info.FirmwareVersion
                    temp_data = get_list_rtsp(onvif_camera = camera,username=username, password=password)
                    if temp_data is not None:
                        for key,data in temp_data.items():
                            if manufacturer is None:
                                manufacturer = brand
                            data.update({'username': username, 'password': password, 'ip': target_url.ip, 'port': 80,'manufacturer': manufacturer,'camera_type':'Onvif','camera_model':model, 'serial_number': serial_number, 'mac_address' : mac_address, 'is_nvr_dvr':is_nvr_dvr, 'number_cameras':number_cameras})
                            # rtsp_output.append(data)
                            if callback_result is not None:
                                callback_result(data)
                    else:
                        username, password = target_url.credentials.split(":")
                        data = {}
                        data.update({'username': username, 'password': password, 'ip': target_url.ip, 'port': 80,'manufacturer': brand,'camera_type':'Not Onvif','camera_model':'', 'serial_number': '', 'mac_address' : mac_address, 'is_nvr_dvr':is_nvr_dvr, 'number_cameras':'1'})
                        callback_result(data)
                        output_queue.put(target_url)
            except exceptions.ONVIFError as e:
                # Không thành công kết nối hoặc không hỗ trợ ONVIF
                print(f"ONVIFError = {e}")
                output_queue.put(target_url)
        input_queue.task_done()
    # print(f'brute_onvif done')

def screenshot_targets(input_queue: Queue,callback_result: Callable) -> None:
    while True:
        target_url: RTSPClient = input_queue.get()
        if target_url is None:
            break
        camera = {}
        camera['username'],camera['password'] = target_url.credentials.split(':')
        camera['ip'] = target_url.ip
        camera['port'] = target_url.port
        camera['profileToken'] = None
        camera['manufacturer'] = None
        camera['is_ptz'] = None
        camera['camera_type'] = None
        camera['camera_model'] = None
        str_target_url = str(target_url)
        # check if "/Streaming/Channels/101/" is in target_url
        if "/Streaming/Channels/101/" in str_target_url:
            # iterate "/Streaming/Channels/101/" to "/Streaming/Channels/1601/"
            for i in range(1, 17):
                main_target_url = str_target_url.replace("/Streaming/Channels/101/", f"/Streaming/Channels/{i}01/")
                image = get_screenshot(main_target_url)
                if image:
                    #with LOCK:
                    copied_camera = camera.copy()
                    copied_camera['rtsp_list'] = [main_target_url]
                    # rtsp_list.append(copied_camera)
                    sub_target_url = str_target_url.replace(f"/Streaming/Channels/101/", f"/Streaming/Channels/{i}02/")
                    image = get_screenshot(sub_target_url)
                    if image:
                        copied_camera['rtsp_list'].append(sub_target_url)
                    if callback_result is not None:
                        callback_result(copied_camera)
                    append_result(image, main_target_url)
        else:
            # check if target_url doesn't have anything after :554/
            if str_target_url.endswith(':554/'):
                # first try the unmodified URL
                image = get_screenshot(str_target_url)
                if image:
                    #with LOCK:
                    camera['rtsp_list'] = [str_target_url]
                    sub_route = target_url.route.get(2,None)
                    if sub_route is not None:
                        target_url = target_url.get_rtsp_url(target_url.ip, target_url.port, target_url.credentials, sub_route)
                        image = get_screenshot(str(target_url))
                        if image:
                            camera['rtsp_list'].append(str(target_url))
                    if callback_result is not None:
                        callback_result(camera)
                    append_result(image, str_target_url)
            else:
                image = get_screenshot(str_target_url)
                if image:
                    #with LOCK:
                    camera['rtsp_list'] = [str_target_url]
                    sub_route = target_url.route.get(2,None)
                    if sub_route is not None:
                        target_url = target_url.get_rtsp_url(target_url.ip, target_url.port, target_url.credentials, sub_route)
                        image = get_screenshot(str(target_url))
                        if image:
                            camera['rtsp_list'].append(str(target_url))

                    if callback_result is not None:
                        callback_result(camera)

                    append_result(image, str_target_url)

        input_queue.task_done()

def check_ptz(camera:ONVIFCamera = None):
    # media_service = camera.create_media_service()
    # profiles = media_service.GetProfiles()
    # for profile in profiles:
    #     if profile['PTZConfiguration']['PanTiltLimits'] is not None:
    #         return True
    # return None
    try:
        ptz = camera.create_ptz_service()
        ptz_node = ptz.GetNodes()
        for item in ptz_node:
            if item['SupportedPTZSpaces'] and item['SupportedPTZSpaces']['AbsolutePanTiltPositionSpace']:
                return True
    except Exception as e:
        return None
def get_list_rtsp(onvif_camera:ONVIFCamera = None, username = None, password = None):
    cameras = {}
    try:
        media_service = onvif_camera.create_media_service()
        profiles = media_service.GetProfiles()
        if len(profiles) != 0:
            try:
                video_encoder_configurations = media_service.GetVideoEncoderConfigurations()
            except Exception as e:
                video_encoder_configurations = None
                return None
            for index,profile in enumerate(profiles):
                # print(f'profile = {profile}')
                # break
                video_source_configuration_token = profile.VideoSourceConfiguration.token
                if video_source_configuration_token not in cameras:
                    camera = {'profileToken':profile.token}
                    if len(video_encoder_configurations) != 0:
                        target_encoder_configuration = video_encoder_configurations[index]
                        if target_encoder_configuration.Encoding == "H264":
                            # Lấy các tùy chọn cấu hình encoder
                            options = media_service.GetVideoEncoderConfigurationOptions({'ConfigurationToken': target_encoder_configuration.token})
                            resolutions = []
                            for item in options.H264.ResolutionsAvailable:
                                resolution = {'width': item.Width,'height': item.Height}
                                resolutions.append(resolution)
                            camera['supportedMainResolution'] = json.dumps(resolutions)
                            camera['mainstreamResolution'] = str(target_encoder_configuration.Resolution.Width) + 'x' + str(target_encoder_configuration.Resolution.Height)

                            fps = []
                            if options.H264.FrameRateRange != None:
                                fps_min = options.H264.FrameRateRange['Min']
                                fps_max = options.H264.FrameRateRange['Max']
                                for i in range(fps_min, fps_max + 1):
                                    fps.append(i)
                                camera['supportedMainFps'] = json.dumps(fps)
                            camera['mainstreamFps'] = json.dumps(target_encoder_configuration.RateControl.FrameRateLimit)
                    stream_setup = {
                        'StreamSetup': {
                            'Stream': 'RTP-Unicast',
                            'Transport': {
                                'Protocol': 'RTSP'
                            }
                        },
                        'ProfileToken': profile.token
                    }
                    stream_uri = media_service.GetStreamUri(stream_setup)
                    rtsp = stream_uri.Uri
                    rtsp = rtsp.replace("rtsp://", f"rtsp://{username}:{password}@")
                    camera['rtsp_list'] = [rtsp]

                    if profile.PTZConfiguration is not None:
                        if profile.PTZConfiguration.DefaultPTZSpeed is not None or profile.PTZConfiguration.PanTiltLimits is not None or profile.PTZConfiguration.ZoomLimits is not None:
                            camera['is_ptz'] = True
                        else:
                            camera['is_ptz'] = None
                    else:
                        camera['is_ptz'] = None
                    # is_continuous_move = check_continuous_move(camera = onvif_camera,profileToken=profile.token)
                    # camera['is_continuous_move'] = is_continuous_move
                    cameras[video_source_configuration_token] = camera
                else:
                    try:
                        camera = cameras[video_source_configuration_token]
                        # logger.info(f"ahihi = {len(video_encoder_configurations),index}")
                            # chi get them 1 luong substream thoi
                        if len(video_encoder_configurations) != 0:
                            target_encoder_configuration = video_encoder_configurations[index]
                            if target_encoder_configuration.Encoding == "H264":
                                # Lấy các tùy chọn cấu hình encoder
                                options = media_service.GetVideoEncoderConfigurationOptions({'ConfigurationToken': target_encoder_configuration.token})
                                resolutions = []
                                for item in options.H264.ResolutionsAvailable:
                                    resolution = {'width': item.Width,'height': item.Height}
                                    resolutions.append(resolution)
                                camera['supportedSubResolution'] = json.dumps(resolutions)
                                camera['substreamResolution'] = str(target_encoder_configuration.Resolution.Width) + 'x' + str(target_encoder_configuration.Resolution.Height)
                                fps = []
                                if options.H264.FrameRateRange != None:
                                    fps_min = options.H264.FrameRateRange['Min']
                                    fps_max = options.H264.FrameRateRange['Max']
                                    for i in range(fps_min, fps_max + 1):
                                        fps.append(i)
                                    camera['supportedSubFps'] = json.dumps(fps)
                                camera['substreamFps'] = json.dumps(target_encoder_configuration.RateControl.FrameRateLimit)
                                
                        if len(camera['rtsp_list']) == 1:
                            stream_setup = {
                                'StreamSetup': {
                                    'Stream': 'RTP-Unicast',
                                    'Transport': {
                                        'Protocol': 'RTSP'
                                    }
                                },
                                'ProfileToken': profile.token
                            }
                            stream_uri = media_service.GetStreamUri(stream_setup)
                            rtsp = stream_uri.Uri
                            rtsp = rtsp.replace("rtsp://", f"rtsp://{username}:{password}@")
                            camera['rtsp_list'].append(rtsp)
                    except Exception as e:
                        logging.info(f'error: {e}')
        return cameras
    except Exception as e:
        logger.info(f'error get_list_rtsp {e}')
    return None

def check_continuous_move(camera: ONVIFCamera= None,profileToken = None):
    try:
        ptz = camera.create_ptz_service()
        ptz_request = {}
        ptz_request["ProfileToken"] = profileToken
        ptz_request['Velocity'] = {}
        ptz_request['Velocity']['PanTilt'] = {}
        ptz_request['Velocity']['PanTilt']['x'] = 0.1
        ptz_request['Velocity']['PanTilt']['y'] = 0.1
        ptz_request['Velocity']['PanTilt']['space'] = 'http://www.onvif.org/ver10/tptz/PanTiltSpaces/VelocityGenericSpace'
        ptz_request['Velocity']['Zoom'] = None
        ptz_request['Timeout'] = None
        # logger.info(f'control_ptz = {ptz_request}')
        a = ptz.ContinuousMove(ptz_request)
        # if name_move == 'around':
        #     sleep(2)
        # else:
        time.sleep(0.02)
        ptz.Stop({'ProfileToken': profileToken})
        return True
    except Exception as e:
        print(f'checkout_continuous_move {e}')
        return None
