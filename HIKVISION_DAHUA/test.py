# from dahua_rpc import DahuaRpc
import hikvision.api

# This will use http by default (not https)
# pass False to the digest_auth parameter of CreateDevice to fallback to basic auth
# (note that basic auth and http without ssl are inherently insecure)
# more recent hikvision firmwares default to turning basic auth off
# (and that's a good idea for security)
# rtsp://admin:abcd1234@*************:554/Streaming/Channels/101/
hik_camera = hikvision.api.CreateDevice('14.162.163.203', username='admin', password='abcd1234')
xml_data = hik_camera.get_about()
print(f'xml_data: {xml_data} - type: {type(xml_data)}')
# # hik_camera.get_version()
# # hik_camera.get_channels_useful()
number_cameras = hik_camera.get_channels_inputproxy()
print(f'Number Cameras: {number_cameras}')



# Response: <?xml version="1.0" encoding="UTF-8" ?>
# <DeviceInfo version="1.0" xmlns="http://www.hikvision.com/ver20/XMLSchema">
# <deviceName>Network Video Recorder</deviceName>
# <deviceID>48443533-3836-3937-3637-686dbcf04d48</deviceID>
# <model>DS-7616NI-K1(B)</model>
# <serialNumber>DS-7616NI-K1(B)1620190824CCRRD53869767WCVU</serialNumber>
# <macAddress>68:6d:bc:f0:4d:48</macAddress>
# <firmwareVersion>V3.4.100</firmwareVersion>
# <firmwareReleasedDate>build 190815</firmwareReleasedDate>
# <encoderVersion>V5.0</encoderVersion>
# <encoderReleasedDate>build 171115</encoderReleasedDate>
# <deviceType>IPC</deviceType>
# <telecontrolID>255</telecontrolID>
# <customizedInfo>DZR20190814001</customizedInfo>
# </DeviceInfo>

# get deviceName and model and macAddress
# device_name, model, mac_address = hik_camera.get_device_name(xml_data)
# print(f'device_name, model, mac_address: {device_name} - {model} - {mac_address}')


# dahua = DahuaRpc(host="*************", username="admin", password="abcd1234")
# dahua.login()

# product_definition = dahua.request(method="magicBox.getProductDefinition")
# serial_number = dahua.request(method="magicBox.getSerialNo")
# device_type = dahua.request(method="magicBox.getDeviceType")
# machine_name = dahua.request(method="magicBox.getMachineName")
# system_info = dahua.request(method="magicBox.getSystemInfo")
# # getCaps  = dahua.request(method="magicBox.getDeviceCaps")
# # print(f'getCaps : {getCaps}')
# print(f'product_definition: {product_definition}')
# print(f'serial_number: {serial_number}')
# print(f'device_type: {device_type}')
# print(f'machine_name: {machine_name}')
# print(f'system_info: {system_info}')

# # # Extract and print the relevant information
# device_type_value = device_type.get("params", {}).get("type", "Unknown Type")
# machine_name_value = machine_name.get("params", {}).get("name", "Unknown Name")
# serial_number_value = serial_number.get("params", {}).get("sn", "Unknown Serial")
# if device_type_value.contains('XVR') or device_type_value.contains('DVR') or model.contains('NVR'):
#     number_cameras = data['params']['definition']['VideoInputChannels']
# else:
#     number_cameras = '1'
# print(f"Device Type: {device_type_value}")
# print(f"Machine Name: {machine_name_value}")
# print(f"Serial Number: {serial_number_value}")