import os
import openpyxl
import hikvision.api
import subprocess
import re
from dahua_rpc import DahuaRpc
from rtsp_python.rtsp_python import RtspPython
from pydantic import BaseModel, HttpUrl, ValidationError
from pydantic_core import Url
# Define a Pydantic model for the camera data
class CameraData(BaseModel):
    ip: str
    user: str
    password: str
    endpoint: str
    port: str

class CameraDataResult(BaseModel):
    ip: str
    user: str
    password: str
    endpoint: str
    port: str
    brand: str
    model: str
    mac_address: str
    serial_number: str
    nvr: bool
    number_cameras: str


# List of known camera brands
camera_brands = [
    "Hikvision", "Swann", "Lorex", "Uniden", "Logitech", "Netgear", "Viviotek", "Axis", "Defender",
    "Netatmo", "Nest", "Foscam", "Sengled", "Samsung", "D-Link", "Panasonic", "Dahua", 
    "Bushnell", "Zmodo", "Avigilon", "<PERSON>sch", "Sony", "Reolink", "Amcrest", "<PERSON>rlo",
    "Ring", "TP-Link", "Ubiquiti", "Wansview", "Yi", "Zmodo", "Zosi", "Xiaomi", "Ezviz", "Anran",
    "TVT", "Q-See", "Night Owl", "LaView", "Jooan", "Hiseeu", "Faleemi", "Sricam", "Sannce", "Smonet",
    "Zclever", "Zosi", "Zmodo", "Zxtech", "Zycoo", "IDIS", "UNV", "Uniview", "Milestone", "Maxxess", "Hanwha",
    "Techwin", "MOBOTIX", "FLIR", "GeoVision", "Genetec", "DVTel", "Axxon", "Shenzhen", "Tiandy", "Netview", "ProVision"
]

nvr_dvr_list = [
    "NVR", "DVR", "Network Video Recorder", "network video recorder"
]

cameras_result = {}

def entry_exists(ws, ip, user, model):
    """
    Check if the entry with the given IP, Username, and MAC Address already exists in the worksheet.
    """
    for row in ws.iter_rows(min_row=2, values_only=True):  # Skip the header
        if model == '':
            model = None
        if user == '':
            user = None
        if ip == '':
            ip = None
        if (row[0] == ip and row[1] == user and row[6] == model):
            return True
    return False

def create_excel_file(file_name="camera_data.xlsx"):
    # Check if the Excel file already exists
    if os.path.exists(file_name):
        # Open the existing workbook
        wb = openpyxl.load_workbook(file_name)
        ws = wb.active
    else:
        # Create a new workbook and select the active worksheet
        wb = openpyxl.Workbook()
        ws = wb.active

        # Write the header row (only for new files)
        headers = ["IP", "Username", "Password", "Endpoint", "Number of Cameras", "Brand", "Model", "MAC Address", "Serial Number", "NVR/DVR"]
        ws.append(headers)

    # Write the data rows, skipping duplicates
    for result in cameras_result.values():
        if result is not None:
            # Check if the entry already exists in the Excel file
            if not entry_exists(ws, result.ip, result.user, result.model):
                ws.append([
                    result.ip,
                    result.user,
                    result.password,
                    result.endpoint,
                    result.number_cameras,
                    result.brand,
                    result.model,
                    result.mac_address,
                    result.serial_number,
                    result.nvr
                ])

    # Save the Excel file
    wb.save(file_name)
    print(f"Data written to {file_name} successfully, skipping duplicates.")

# Function to scan a specific IP with nmap and return the camera brand and MAC address
def scan_camera(ip_address, options_scan="-sS", found_brand = None, mac_address = None, is_dvr_nvr = False):
    try:
        # Running nmap scan to detect brand and MAC address
        result = subprocess.run(["nmap", options_scan, "-p80,554", "-n", "-T4", "-O", ip_address], capture_output=True, text=True)
        output = result.stdout
        print(f"Scanning {ip_address} with options: {options_scan}")
        print(f'output: {output}')
        # Search for any known camera brand in the output
        if found_brand is None:
            for brand in camera_brands:
                if re.search(brand, output, re.IGNORECASE):
                    print(f"Camera found: {brand} on {ip_address}")
                    found_brand = brand
                    break
        
        if is_dvr_nvr is False:
            for nvr_dvr in nvr_dvr_list:
                if re.search(nvr_dvr, output, re.IGNORECASE):
                    print(f"Is NVR/DVR found: {nvr_dvr} on {ip_address}")
                    is_dvr_nvr = True
                    break

        # Search for the MAC address in the output
        if mac_address is None:
            mac_address = re.search(r"MAC Address: ([\w:]+)", output)
            if mac_address:
                mac_address = mac_address.group(1)
            # print(f"MAC Address: {mac_address}")
        else:
            print(f"No MAC address found on {ip_address}")
            if options_scan == "-sS":
                return scan_camera(ip_address, "-sV", found_brand, mac_address, is_dvr_nvr)  # Retry with no ping scan
            if options_scan == "-sV":
                return scan_camera(ip_address, "-sP", found_brand, mac_address, is_dvr_nvr)  # Retry with no version detection
            if options_scan == "-sP":
                print(f"Could not find MAC address for {ip_address}")
                return found_brand, None, is_dvr_nvr
        
        # print(f'---')
        return found_brand, mac_address if mac_address else None, is_dvr_nvr

    except Exception as e:
        print(f"Error during scanning: {e}")
        return found_brand, mac_address, is_dvr_nvr
    
def callback_result(data):
    # print(f'callback_result = {data}')
    # callback_result = {'profileToken': 'MediaProfile00600', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=7&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=7&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'username': 'admin', 'password': 'abcd1234', 'ip': '**************', 'port': 80, 'manufacturer': 'Dahua', 'camera_type': 'Onvif', 'camera_model': 'DH-XVR4116HS-X', 'serial_number': '6A02CC8PAZ2354C'}
    ip = data.get('ip')
    user = data.get('username')
    password = data.get('password')
    port = data.get('port')
    brand = data.get('manufacturer')
    model = data.get('camera_model')
    serial_number = data.get('serial_number')
    nvr = data.get('is_nvr_dvr')
    number_cameras = data.get('number_cameras', '1')
    print(f'ip: {ip} - user: {user} - password: {password} - brand: {brand} - model: {model} - serial_number: {serial_number}')
    result = CameraDataResult(ip=str(ip), user=str(user), password=str(password), endpoint='', port=str(port), brand=str(brand), model=str(model), mac_address='', serial_number=str(serial_number), nvr=str(nvr), number_cameras=str(number_cameras))
    if result.ip in cameras_result and result.serial_number in cameras_result:
        pass
    else:
        index_value = f'{str(result.ip)}_{str(result.serial_number)}'
        cameras_result[index_value] = result
    create_excel_file()



def scan_to_get_camera_info(camera_data: CameraData):
    number_cameras = None
    device_name = None
    model = None
    mac_address = None
    serial_number = None
    is_nvr = False
    found_brand = None
    is_nvr_dvr = False
    mac_address = None
    found_brand, mac_address, is_nvr_dvr = scan_camera(camera_data.ip)
    print(f'ip_addresses = {found_brand, mac_address}')
    if found_brand == "Hikvision" or camera_data.endpoint.__contains__('/Streaming/Channels'):
        print(f'has {camera_data.endpoint.__contains__('/Streaming/Channels')} ')
        try:
            hik_camera = hikvision.api.CreateDevice(camera_data.ip, username=camera_data.user, password=camera_data.password)
            xml_data = hik_camera.get_about()
            device_name, model, mac_address, serial_number = hik_camera.get_device_name(xml_data)
            is_nvr = device_name == "Network Video Recorder"
            if is_nvr:
                number_cameras = hik_camera.get_channels_inputproxy()
            return CameraDataResult(ip=str(camera_data.ip), user=str(camera_data.user), password=str(camera_data.password), endpoint=str(camera_data.endpoint), port=str(camera_data.port), brand='Hikvision', model=str(model), mac_address= str(mac_address), serial_number=str(serial_number), nvr=str(is_nvr), number_cameras=str(number_cameras))
        except Exception as e:
            print(f'Hikvision ERROR: {e}')
            return find_camera_by_onvif(camera_data=camera_data, found_brand=found_brand, mac_address=mac_address, is_nvr_dvr=is_nvr_dvr)
    elif found_brand == "Dahua":
        try:
            dahua = DahuaRpc(host=camera_data.ip, username=camera_data.user, password=camera_data.password)
            dahua.login()
            product_definition_xml_data = dahua.request(method="magicBox.getProductDefinition")
            serial_number_xml_data = dahua.request(method="magicBox.getSerialNo")
            device_type_xml_data = dahua.request(method="magicBox.getDeviceType")
            machine_name_xml_data = dahua.request(method="magicBox.getMachineName")
            # Extract and print the relevant information
            model = device_type_xml_data.get("params", {}).get("type", "Unknown Type")
            device_name = machine_name_xml_data.get("params", {}).get("name", "Unknown Name")
            serial_number = serial_number_xml_data.get("params", {}).get("sn", "Unknown Serial")
            if model.contains('XVR') or model.contains('DVR') or model.contains('NVR'):
                number_cameras = product_definition_xml_data.get("params", {}).get("definition", {}).get("VideoInputChannels", "Unknown Channels")
            else:
                number_cameras = '1'
            return CameraDataResult(ip=str(camera_data.ip), user=str(camera_data.user), password=str(camera_data.password), endpoint=str(camera_data.endpoint), port=str(camera_data.port), brand='Dahua', model=str(model), mac_address=str(mac_address), serial_number=str(serial_number), nvr=str(is_nvr_dvr), number_cameras=str(number_cameras))
        except Exception as e:
            print(f'Dahua ERROR: {e}')
            return find_camera_by_onvif(camera_data=camera_data, found_brand=found_brand, mac_address=mac_address, is_nvr_dvr=is_nvr_dvr)
    else:
        return find_camera_by_onvif(camera_data=camera_data, found_brand=found_brand, mac_address=mac_address, is_nvr_dvr=is_nvr_dvr)

def find_camera_by_onvif(camera_data: CameraData, found_brand, mac_address, is_nvr_dvr):
    rtsppython = RtspPython(ip_list=[camera_data.ip],callback_result=callback_result,credential_list=[f'{str(camera_data.user)}:{str(camera_data.password)}'], brand=found_brand, mac_address=mac_address, is_nvr_dvr=is_nvr_dvr)
    rtsppython.scan()
    return None


file_path = "camera_scan.txt"
# Read the actual content from the file
with open(file_path, 'r') as file:
    camera_scan_content = file.read()

# Parse the content again using the same method
list_cameras_links = camera_scan_content.splitlines()
length_cameras = len(list_cameras_links)
for line in list_cameras_links:
    data = Url(line)
    if data is not None:
        scheme = data.scheme
        username = data.username
        password = data.password
        ip = data.host
        port = data.port
        path = data.path
        print(scheme, username, password, ip, port, path)
        camera_data = CameraData(ip=str(ip), user=str(username), password=str(password), port=str(port), endpoint=str(path))
        result = scan_to_get_camera_info(camera_data)
        if result is not None:
            if result.ip in cameras_result and result.serial_number in cameras_result:
                pass
            else:
                print(f'ADD ----- {type(result)}')
                index_value = f'{str(result.ip)}_{str(result.serial_number)}'
                cameras_result[index_value] = result
            create_excel_file()

