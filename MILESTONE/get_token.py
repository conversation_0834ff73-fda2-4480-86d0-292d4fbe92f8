#!/usr/bin/env python3
"""
Script để lấy và in ra token từ Milestone API
"""
import sys
import os
sys.path.append('MILESTONE')

from milestone_api_advanced import MilestoneAPIAdvanced

def get_and_print_token():
    """Lấy và in ra token"""
    print("=== LẤY TOKEN TỪ MILESTONE API ===")
    
    # Khởi tạo API
    api = MilestoneAPIAdvanced("https://**************")
    
    # Đăng nhập
    print("Đang đăng nhập...")
    login_result = api.login("DESKTOP-7SFAJTR\\admin", "Secu@9DS", False)
    
    if login_result["success"]:
        print("✅ Đăng nhập thành công!")
        print(f"🔑 ACCESS TOKEN: {login_result['access_token']}")
        print(f"⏰ TOKEN TYPE: {login_result.get('token_type', 'Bearer')}")
        print(f"⏱️  EXPIRES IN: {login_result['expires_in']} giây")
        
        # In token để copy dễ dàng
        print("\n" + "="*50)
        print("TOKEN ĐỂ COPY:")
        print(login_result['access_token'])
        print("="*50)
        
        return login_result['access_token']
    else:
        print(f"❌ Đăng nhập thất bại: {login_result['message']}")
        return None

if __name__ == "__main__":
    token = get_and_print_token() 