"""
Get a bearer access token from the identity provider
"""
import requests
from requests_ntlm import HttpNtlmAuth


def get_token(
    session: requests.Session,
    username: str,
    password: str,
    serverUrl: str,
    isBasicUser: bool,
) -> str:
    """
    Requests an OAuth 2.0 access token from the identity provider on a VMS server for a VMS user.
    The API Gateway forwards the request to the identity provider

    :param session: A requests.Session object which will be used for the duration of the
        integration to maintain logged-in state
    :param username: The username of an XProtect user with the XProtect Administrators role
    :param password: The password of the user logging in
    :param server: The hostname of the machine hosting the identity provider, e.g. "vms.example.com"
    :param isBasicUser: Defines whether the login should be done using basic authentication

    :returns: session.Response object. The value of the 'access_token' property is the bearer token.

        Note the "expires_in" property; if you're planning on making a larger integration, you will
        have to renew before it has elapsed.
    """

    if isBasicUser:
        return get_token_basic(session, username, password, serverUrl)
    else:
        return get_token_windows(session, username, password, serverUrl)


def get_token_basic(
    session: requests.Session, username: str, password: str, serverUrl: str
) -> str:
    url = f"{serverUrl}/API/IDP/connect/token"
    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    payload = f"grant_type=password&username={username}&password={password}&client_id=GrantValidatorClient"
    return session.request("POST", url, headers=headers, data=payload, verify=False)


def get_token_windows(
    session: requests.Session, username: str, password: str, serverUrl: str
) -> str:
    # Get the token directly from the identity provider as MIP VMS RESTful API gateway doesn't support pass-through of NTLM authentication
    url = f"{serverUrl}/IDP/connect/token"
    print(f'url: {url}')
    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    payload = f"grant_type=windows_credentials&client_id=GrantValidatorClient"
    return session.request(
        "POST",
        url,
        headers=headers,
        data=payload,
        verify=False,
        auth=HttpNtlmAuth(username, password),
    )


# /API/rest/v1/cameras/{id}/streams
def get_camera_streams(
    session: requests.Session, serverUrl: str, cameraId: str, token: str
) -> requests.Response:
    url = f"{serverUrl}/API/rest/v1/cameras/{cameraId}/streams"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
    }
    return session.request("GET", url, headers=headers, verify=False)

# /API/rest/v1/streams/{id}
def get_stream(
    session: requests.Session, serverUrl: str, streamId: str, token: str
) -> requests.Response:
    url = f"{serverUrl}/API/rest/v1/streams/{streamId}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
    }
    return session.request("GET", url, headers=headers, verify=False)

# /API/rest/v1/recordingServers/{id}/hardware
def get_recording_server_hardware(
    session: requests.Session, serverUrl: str, recordingServerId: str, token: str
) -> requests.Response:
    url = f"{serverUrl}/API/rest/v1/recordingServers/{recordingServerId}/hardware"
    print(f'get_recording_server_hardware: url: {url}')
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
    }
    return session.request("GET", url, headers=headers, verify=False)

# /API/rest/v1/recordingServers
def get_recording_servers(
    session: requests.Session, serverUrl: str, token: str
) -> requests.Response:
    url = f"{serverUrl}/API/rest/v1/recordingServers"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
    }
    return session.request("GET", url, headers=headers, verify=False)


# /API/rest/v1/settings/{id}
def get_setting(
    session: requests.Session, serverUrl: str, settingId: str, token: str
) -> requests.Response:
    url = f"{serverUrl}/API/rest/v1/settings/{settingId}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
    }
    return session.request("GET", url, headers=headers, verify=False)


# /API/rest/v1/cameras/{id}/settings
def get_camera_settings(
    session: requests.Session, serverUrl: str, cameraId: str, token: str
) -> requests.Response:
    url = f"{serverUrl}/API/rest/v1/cameras/{cameraId}/settings"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
    }
    return session.request("GET", url, headers=headers, verify=False)