"""
API Milestone wrapper nâng cao với WebRTC và streaming methods
"""
import json
import requests
from typing import Dict, List, Optional, Tuple
import identity_provider
from api_gateway import Gateway


class MilestoneAPIAdvanced:
    """
    Class API Milestone nâng cao với WebRTC và multiple streaming methods
    """
    
    def __init__(self, server_url: str):
        """
        Khởi tạo API
        
        :param server_url: URL của server Milestone (ví dụ: "https://**************")
        """
        self.server_url = server_url
        self.session = requests.Session()
        self.access_token = None
        self.token_expires_in = None
        self.api_gateway = Gateway(server_url)
        
    def login(self, username: str, password: str, is_basic_user: bool = False) -> Dict:
        """
        Đăng nhập vào hệ thống Milestone
        """
        try:
            print(f"Đang đăng nhập với user: {username}")
            response = identity_provider.get_token(
                self.session, username, password, self.server_url, is_basic_user
            )
            
            if response.status_code == 200:
                token_response = response.json()
                self.access_token = token_response["access_token"]
                self.token_expires_in = token_response.get("expires_in", 3600)
                
                return {
                    "success": True,
                    "message": "Đăng nhập thành công",
                    "access_token": self.access_token,
                    "expires_in": self.token_expires_in,
                    "token_type": token_response.get("token_type")
                }
            else:
                error = response.json().get("error", "Lỗi không xác định")
                return {
                    "success": False,
                    "message": f"Đăng nhập thất bại: {error}",
                    "error": error
                }
        except Exception as e:
            return {
                "success": False,
                "message": f"Lỗi khi đăng nhập: {str(e)}",
                "error": str(e)
            }
    
    def get_cameras(self) -> Dict:
        """
        Lấy danh sách tất cả camera với thông tin chi tiết
        """
        if not self.access_token:
            return {
                "success": False,
                "message": "Chưa đăng nhập. Vui lòng gọi login() trước.",
                "cameras": []
            }
        
        try:
            response = self.api_gateway.get(self.session, 'cameras', self.access_token)
            
            if response.status_code == 200:
                cameras_data = response.json()
                cameras = cameras_data.get('array', [])
                
                # Xử lý và format dữ liệu camera với thông tin streaming
                formatted_cameras = []
                for camera in cameras:
                    # Lấy stream info cho camera
                    stream_info = self._get_camera_stream_details(camera.get("id"))
                    
                    formatted_camera = {
                        "id": camera.get("id"),
                        "name": camera.get("name"),
                        "displayName": camera.get("displayName"),
                        "enabled": camera.get("enabled"),
                        "channel": camera.get("channel"),
                        "description": camera.get("description"),
                        "recordingEnabled": camera.get("recordingEnabled"),
                        "parentHardware": camera.get("relations", {}).get("parent", {}).get("id"),
                        "streamInfo": stream_info,
                        "capabilities": {
                            "liveStreaming": True,
                            "playback": camera.get("recordingEnabled", False),
                            "ptz": self._check_ptz_capability(camera.get("id"))
                        }
                    }
                    formatted_cameras.append(formatted_camera)
                
                return {
                    "success": True,
                    "message": f"Lấy được {len(formatted_cameras)} camera",
                    "count": len(formatted_cameras),
                    "cameras": formatted_cameras
                }
            else:
                return {
                    "success": False,
                    "message": f"Lỗi khi lấy danh sách camera: {response.status_code}",
                    "cameras": []
                }
        except Exception as e:
            return {
                "success": False,
                "message": f"Lỗi khi lấy danh sách camera: {str(e)}",
                "cameras": []
            }
    
    def _get_camera_stream_details(self, camera_id: str) -> Dict:
        """
        Lấy thông tin chi tiết stream của camera
        """
        try:
            stream_response = identity_provider.get_camera_streams(
                self.session, self.server_url, camera_id, self.access_token
            )
            
            if stream_response.status_code == 200:
                stream_data = stream_response.json()
                streams = stream_data.get('array', [])
                
                if streams:
                    stream = streams[0]
                    stream_list = stream.get('stream', [])
                    if stream_list:
                        stream_details = stream_list[0]
                        return {
                            "streamReferenceId": stream_details.get('streamReferenceId'),
                            "displayName": stream_details.get('displayName'),
                            "name": stream_details.get('name'),
                            "liveDefault": stream_details.get('liveDefault'),
                            "liveMode": stream_details.get('liveMode'),
                            "useEdge": stream_details.get('useEdge')
                        }
            return {}
        except:
            return {}
    
    def _check_ptz_capability(self, camera_id: str) -> bool:
        """
        Kiểm tra camera có hỗ trợ PTZ không
        """
        try:
            # Thử lấy PTZ tasks để kiểm tra capability
            response = self.api_gateway.get_child_item_tasks(
                self.session, "cameras", camera_id, "ptzpresets", self.access_token
            )
            return response.status_code == 200
        except:
            return False
    
    def get_groups(self) -> Dict:
        """
        Lấy danh sách các group (viewGroups, userGroups, cameraGroups)
        """
        if not self.access_token:
            return {
                "success": False,
                "message": "Chưa đăng nhập. Vui lòng gọi login() trước.",
                "groups": []
            }
        
        try:
            groups_result = {
                "viewGroups": [],
                "userGroups": [],
                "cameraGroups": []
            }
            
            # Lấy các loại groups
            for group_type in ["viewGroups", "userGroups", "cameraGroups"]:
                try:
                    response = self.api_gateway.get(self.session, group_type, self.access_token)
                    if response.status_code == 200:
                        groups = response.json().get('array', [])
                        for group in groups:
                            formatted_group = {
                                "id": group.get("id"),
                                "name": group.get("name"),
                                "displayName": group.get("displayName"),
                                "type": group_type[:-1],  # Remove 's' from end
                                "description": group.get("description", ""),
                                "enabled": group.get("enabled", True)
                            }
                            groups_result[group_type].append(formatted_group)
                except:
                    continue
            
            total_groups = sum(len(groups_result[key]) for key in groups_result)
            
            return {
                "success": True,
                "message": f"Lấy được {total_groups} groups",
                "count": total_groups,
                "groups": groups_result
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Lỗi khi lấy danh sách groups: {str(e)}",
                "groups": []
            }
    
    def get_all_streaming_urls(self, camera_id: str, hostname: str = "desktop-mcv8ica", port: int = 7563) -> Dict:
        """
        Lấy tất cả các loại streaming URL có thể cho camera
        
        :param camera_id: ID của camera
        :param hostname: Hostname của image server
        :param port: Port của image server
        :return: Dict chứa tất cả streaming URLs
        """
        if not self.access_token:
            return {
                "success": False,
                "message": "Chưa đăng nhập. Vui lòng gọi login() trước.",
                "streaming_urls": {}
            }
        
        try:
            # Lấy thông tin stream
            stream_response = identity_provider.get_camera_streams(
                self.session, self.server_url, camera_id, self.access_token
            )
            
            if stream_response.status_code != 200:
                return {
                    "success": False,
                    "message": f"Không thể lấy thông tin stream: {stream_response.status_code}",
                    "streaming_urls": {}
                }
            
            stream_data = stream_response.json()
            streams = stream_data.get('array', [])
            
            if not streams:
                return {
                    "success": False,
                    "message": "Camera này không có stream nào",
                    "streaming_urls": {}
                }
            
            # Lấy stream đầu tiên
            stream = streams[0]
            stream_info = stream.get('stream', [])
            
            if not stream_info:
                return {
                    "success": False,
                    "message": "Không tìm thấy stream details",
                    "streaming_urls": {}
                }
            
            stream_details = stream_info[0]
            stream_reference_id = stream_details.get('streamReferenceId')
            
            # Tạo tất cả các loại streaming URLs
            streaming_urls = {
                # Image Server Connection
                "image_server": {
                    "url": f"http://{hostname}:{port}",
                    "type": "tcp_socket",
                    "description": "Direct TCP connection to Image Server",
                    "supports": ["live", "playback", "ptz"]
                },
                
                # Milestone Protocol
                "milestone_protocol": {
                    "url": f"milestone://{hostname}/?cameraId={camera_id}",
                    "type": "milestone_protocol",
                    "description": "Milestone protocol for VLC/media players",
                    "supports": ["live", "playback"]
                },
                
                # HTTP Stream via API Gateway
                "http_stream": {
                    "url": f"{self.server_url}/API/rest/v1/cameras/{camera_id}/streams/{stream_reference_id}",
                    "type": "http_rest",
                    "description": "HTTP REST API endpoint",
                    "supports": ["metadata", "configuration"]
                },
                
                # WebRTC (cần tạo session)
                "webrtc": {
                    "url": f"{self.server_url}/api/webRTC/session",
                    "type": "webrtc",
                    "description": "WebRTC peer-to-peer streaming",
                    "supports": ["live", "playback", "ptz"],
                    "session_required": True
                }
            }
            
            return {
                "success": True,
                "message": "Lấy tất cả streaming URLs thành công",
                "camera_id": camera_id,
                "streaming_urls": streaming_urls,
                "stream_details": {
                    "streamReferenceId": stream_reference_id,
                    "displayName": stream_details.get('displayName'),
                    "name": stream_details.get('name'),
                    "liveDefault": stream_details.get('liveDefault'),
                    "liveMode": stream_details.get('liveMode')
                }
            }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Lỗi khi lấy streaming URLs: {str(e)}",
                "streaming_urls": {}
            }
    
    def create_webrtc_session(self, camera_id: str, stream_id: str = None, playback_options: Dict = None) -> Dict:
        """
        Tạo WebRTC session cho camera
        
        :param camera_id: ID của camera
        :param stream_id: ID của stream cụ thể (optional)
        :param playback_options: Tùy chọn playback {timestamp, speed, skipGaps}
        :return: Dict chứa thông tin WebRTC session
        """
        if not self.access_token:
            return {
                "success": False,
                "message": "Chưa đăng nhập. Vui lòng gọi login() trước."
            }
        
        try:
            # Tạo payload cho WebRTC session
            payload = {
                "cameraId": camera_id
            }
            
            if stream_id:
                payload["streamId"] = stream_id
            
            if playback_options:
                payload["playback"] = playback_options
            
            # Tạo WebRTC session
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            response = self.session.post(
                f"{self.server_url}/api/webRTC/session",
                headers=headers,
                json=payload,
                verify=False
            )
            
            if response.status_code == 200:
                session_data = response.json()
                
                return {
                    "success": True,
                    "message": "Tạo WebRTC session thành công",
                    "session_data": session_data,
                    "next_steps": [
                        "1. Tạo Answer SDP từ Offer SDP",
                        "2. PATCH session với Answer SDP",
                        "3. Exchange ICE candidates",
                        "4. Establish UDP connection"
                    ]
                }
            else:
                return {
                    "success": False,
                    "message": f"Lỗi khi tạo WebRTC session: {response.status_code}",
                    "error": response.text
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Lỗi khi tạo WebRTC session: {str(e)}"
            }
    
    def get_webrtc_ice_candidates(self, session_id: str) -> Dict:
        """
        Lấy ICE candidates cho WebRTC session
        
        :param session_id: ID của WebRTC session
        :return: Dict chứa ICE candidates
        """
        if not self.access_token:
            return {
                "success": False,
                "message": "Chưa đăng nhập. Vui lòng gọi login() trước."
            }
        
        try:
            headers = {
                "Authorization": f"Bearer {self.access_token}"
            }
            
            response = self.session.get(
                f"{self.server_url}/api/webRTC/iceCandidates/{session_id}",
                headers=headers,
                verify=False
            )
            
            if response.status_code == 200:
                ice_candidates = response.json()
                
                return {
                    "success": True,
                    "message": "Lấy ICE candidates thành công",
                    "ice_candidates": ice_candidates
                }
            else:
                return {
                    "success": False,
                    "message": f"Lỗi khi lấy ICE candidates: {response.status_code}",
                    "error": response.text
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Lỗi khi lấy ICE candidates: {str(e)}"
            }
    
    def send_ptz_command(self, camera_id: str, command: str, params: Dict = None) -> Dict:
        """
        Gửi PTZ command (cần WebRTC session hoặc other method)
        
        :param camera_id: ID của camera
        :param command: PTZ command (ptzMoveStart, ptzMoveStop, ptzMove, etc.)
        :param params: Parameters cho command
        :return: Dict chứa kết quả
        """
        if not self.access_token:
            return {
                "success": False,
                "message": "Chưa đăng nhập. Vui lòng gọi login() trước."
            }
        
        try:
            # Kiểm tra PTZ capability
            if not self._check_ptz_capability(camera_id):
                return {
                    "success": False,
                    "message": "Camera này không hỗ trợ PTZ"
                }
            
            # Tạo PTZ command payload
            ptz_payload = {
                "ApiVersion": "1.0",
                "type": "request",
                "method": command
            }
            
            if params:
                ptz_payload["params"] = params
            
            return {
                "success": True,
                "message": "PTZ command đã được chuẩn bị",
                "command_payload": ptz_payload,
                "note": "Command này cần được gửi qua WebRTC data channel với label 'commands'"
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Lỗi khi tạo PTZ command: {str(e)}"
            }
    
    def get_camera_presets(self, camera_id: str) -> Dict:
        """
        Lấy danh sách presets của camera PTZ
        
        :param camera_id: ID của camera
        :return: Dict chứa danh sách presets
        """
        if not self.access_token:
            return {
                "success": False,
                "message": "Chưa đăng nhập. Vui lòng gọi login() trước."
            }
        
        try:
            # Thử lấy PTZ presets thông qua task
            payload = json.dumps({"sessionDataId": 0})
            response = self.api_gateway.perform_child_task(
                self.session, "cameras", camera_id, "ptzpresets", "GetDevicePresets", payload, self.access_token
            )
            
            if response.status_code == 200:
                task_result = response.json()["result"]
                
                return {
                    "success": True,
                    "message": "Lấy PTZ presets thành công",
                    "task_result": task_result,
                    "note": "Cần poll task status để lấy kết quả cuối cùng"
                }
            else:
                return {
                    "success": False,
                    "message": f"Lỗi khi lấy PTZ presets: {response.status_code}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Lỗi khi lấy PTZ presets: {str(e)}"
            }
    
    def refresh_token(self, username: str, password: str, is_basic_user: bool = False) -> Dict:
        """
        Refresh access token
        """
        return self.login(username, password, is_basic_user)
    
    def get_system_health(self) -> Dict:
        """
        Lấy thông tin health của hệ thống
        """
        try:
            # Kiểm tra well-known URIs
            response = self.session.get(
                f"{self.server_url}/api/.well-known/uris",
                verify=False
            )
            
            if response.status_code == 200:
                well_known = response.json()
                
                # Lấy thêm system info
                system_info = self.get_system_info()
                
                return {
                    "success": True,
                    "message": "System health OK",
                    "well_known_uris": well_known,
                    "system_info": system_info.get("system_info", {}),
                    "token_status": {
                        "has_token": bool(self.access_token),
                        "expires_in": self.token_expires_in
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"System health check failed: {response.status_code}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Error checking system health: {str(e)}"
            }
    
    def get_system_info(self) -> Dict:
        """
        Lấy thông tin tổng quan về hệ thống
        """
        if not self.access_token:
            return {
                "success": False,
                "message": "Chưa đăng nhập. Vui lòng gọi login() trước."
            }
        
        try:
            system_info = {}
            
            # Lấy thông tin recording servers
            try:
                response = identity_provider.get_recording_servers(
                    self.session, self.server_url, self.access_token
                )
                if response.status_code == 200:
                    system_info["recording_servers"] = response.json().get('array', [])
            except:
                system_info["recording_servers"] = []
            
            # Lấy số lượng cameras
            cameras_info = self.get_cameras()
            system_info["total_cameras"] = cameras_info.get("count", 0)
            
            # Lấy số lượng groups
            groups_info = self.get_groups()
            system_info["total_groups"] = groups_info.get("count", 0)
            
            return {
                "success": True,
                "message": "Lấy thông tin hệ thống thành công",
                "system_info": system_info
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Lỗi khi lấy thông tin hệ thống: {str(e)}"
            }


def demo_advanced_api():
    """
    Demo API nâng cao với WebRTC
    """
    print("=== DEMO MILESTONE API ADVANCED ===")
    
    # Khởi tạo API
    api = MilestoneAPIAdvanced("https://**************")
    
    # 1. Đăng nhập
    print("\n1. Đăng nhập...")
    login_result = api.login("DESKTOP-7SFAJTR\\admin", "Secu@9DS", False)
    print(f"Kết quả: {login_result['message']}")
    
    if not login_result["success"]:
        return
    
    # 2. System health check
    print("\n2. Kiểm tra system health...")
    health_result = api.get_system_health()
    print(f"Kết quả: {health_result['message']}")
    
    # 3. Lấy cameras với stream info
    print("\n3. Lấy danh sách camera với stream info...")
    cameras_result = api.get_cameras()
    
    if cameras_result["success"] and cameras_result["cameras"]:
        first_camera = cameras_result["cameras"][1]
        print(f"Camera: {first_camera['displayName']}")
        print(f"Stream Info: {first_camera['streamInfo']}")
        print(f"Capabilities: {first_camera['capabilities']}")
        
        # 4. Lấy tất cả streaming URLs
        print(f"\n4. Lấy tất cả streaming URLs...")
        streaming_result = api.get_all_streaming_urls(first_camera['id'])
        
        if streaming_result["success"]:
            urls = streaming_result["streaming_urls"]
            print("Có sẵn các loại streaming:")
            for url_type, url_info in urls.items():
                print(f"  - {url_type}: {url_info['url']}")
                print(f"    Type: {url_info['type']}")
                print(f"    Supports: {url_info['supports']}")
        
        # 5. Tạo WebRTC session
        print(f"\n5. Tạo WebRTC session...")
        webrtc_result = api.create_webrtc_session(first_camera['id'])
        print(f"Kết quả WebRTC: {webrtc_result['message']}")
        
        # 6. Test PTZ nếu có
        if first_camera['capabilities']['ptz']:
            print(f"\n6. Test PTZ commands...")
            ptz_result = api.send_ptz_command(first_camera['id'], "ptzMoveStart", {
                "pan": 1,
                "tilt": 0, 
                "zoom": 0,
                "panSpeed": 0.5,
                "tiltSpeed": 0.5,
                "zoomSpeed": 0.5
            })
            print(f"PTZ result: {ptz_result['message']}")


if __name__ == "__main__":
    demo_advanced_api()